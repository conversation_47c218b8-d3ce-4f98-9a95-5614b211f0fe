-- @SET @user_id = '1711958416497926144';

SELECT * FROM fn_rmsv3_members WHERE is_deleted = 0

SELECT *
FROM
    fn_rmsv3_members_type_relate_binding
WHERE
    is_deleted = 0;

SELECT * FROM fn_rmsv3_members_type;

SELECT * FROM fn_rmsv3_members_type_relate WHERE is_deleted = 0;

SELECT *
FROM
    fn_rmsv3_members_type_relate_binding
WHERE
    is_deleted = 0;

-- 用户所属区域
SELECT m.realname, r.level, b.region_pid, b.region_id, b.region_cid, t.title
FROM
    fn_rmsv3_members_type_relate r
    left join fn_rmsv3_members_type t on t.id = r.type_id
    left JOIN fn_rmsv3_members_type_relate_binding b on b.relate_id = r.id
    left join fn_rmsv3_members m on m.id = r.member_id
WHERE
    r.is_deleted = 0
    AND b.is_deleted = 0
    AND m.is_deleted = 0
    AND m.user_id = "1713759728019324928"
    AND m.organization_id = "1711957904168636416";


-- 事件
SELECT e.*, p.*
FROM
    aiot_event e
    LEFT JOIN sporadic_project p ON p.id = e.project_id
WHERE
    e.is_deleted = 0
    AND p.is_deleted = 0
    and p.region_cid in (
        832789087816819449,
        832789087816819448
    )