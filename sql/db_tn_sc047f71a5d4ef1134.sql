/*
 Navicat Premium Data Transfer

 Source Server         : test
 Source Server Type    : MySQL
 Source Server Version : 50726 (5.7.26-log)
 Source Host           : *************:33306
 Source Schema         : db_tn_sc047f71a5d4ef1134

 Target Server Type    : MySQL
 Target Server Version : 50726 (5.7.26-log)
 File Encoding         : 65001

 Date: 20/07/2025 20:19:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aim_firm
-- ----------------------------
DROP TABLE IF EXISTS `aim_firm`;
CREATE TABLE `aim_firm` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `firm_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '厂商名称',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：1-是 0-否',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1593714031064887297 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='人工智能管理厂商表';

-- ----------------------------
-- Table structure for aim_firm_scene_management
-- ----------------------------
DROP TABLE IF EXISTS `aim_firm_scene_management`;
CREATE TABLE `aim_firm_scene_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `firm_id` bigint(20) DEFAULT NULL COMMENT '厂商id',
  `code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '厂商场景编号',
  `scene_name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '厂商场景名称',
  `scene_num` int(11) DEFAULT '0' COMMENT '关联AI场景数',
  `directions` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景介绍',
  `pics` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片（逗号分隔）',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：1-是 0-否',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `is_deleted` (`is_deleted`) USING BTREE,
  KEY `code` (`code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1709312710222630913 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='厂商场景管理';

-- ----------------------------
-- Table structure for aim_scene_category_management
-- ----------------------------
DROP TABLE IF EXISTS `aim_scene_category_management`;
CREATE TABLE `aim_scene_category_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景类别名称',
  `directions` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景类别说明',
  `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：1-是 0-否',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1321335555370061825 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='场景类别管理表';

-- ----------------------------
-- Table structure for aim_scene_firm_relationship
-- ----------------------------
DROP TABLE IF EXISTS `aim_scene_firm_relationship`;
CREATE TABLE `aim_scene_firm_relationship` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `scene_id` bigint(20) DEFAULT NULL COMMENT '场景',
  `firm_id` bigint(20) DEFAULT NULL COMMENT '厂商',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `scene_id` (`scene_id`) USING BTREE,
  KEY `firm_id` (`firm_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1704752406267731969 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='场景-厂商场景关联表';

-- ----------------------------
-- Table structure for aim_scene_management
-- ----------------------------
DROP TABLE IF EXISTS `aim_scene_management`;
CREATE TABLE `aim_scene_management` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景名称',
  `code` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景编号',
  `scene_category_id` bigint(20) DEFAULT '0' COMMENT '场景类别Id',
  `type` tinyint(4) DEFAULT NULL COMMENT '场景类型：0-基本 1-复合',
  `mode` varchar(4) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模式：1-现场宣导 2-事件处置（逗号分隔）',
  `tags` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签（逗号分隔）',
  `pics` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片（逗号分隔）',
  `directions` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景说明',
  `remark` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除：1-是 0-否',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `firm_num` int(11) DEFAULT '0' COMMENT '关联厂商数量',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_scene_category_id` (`scene_category_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1707925522673696769 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='AI场景管理';

-- ----------------------------
-- Table structure for aiot_category
-- ----------------------------
DROP TABLE IF EXISTS `aiot_category`;
CREATE TABLE `aiot_category` (
  `id` bigint(20) NOT NULL,
  `level` tinyint(4) DEFAULT NULL COMMENT '场景分类级别:0-顶级，1-一级',
  `name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景分类名称',
  `code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景分类代码',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='aiot场景分类';

-- ----------------------------
-- Table structure for aiot_event
-- ----------------------------
DROP TABLE IF EXISTS `aiot_event`;
CREATE TABLE `aiot_event` (
  `id` bigint(20) NOT NULL,
  `region_pid` bigint(20) DEFAULT NULL COMMENT '区ID',
  `region_id` bigint(20) DEFAULT NULL COMMENT '街道ID',
  `region_cid` bigint(20) DEFAULT NULL COMMENT '社区ID',
  `project_id` bigint(20) DEFAULT NULL COMMENT '项目ID',
  `event_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件编号',
  `device_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备编码(国标视频认证编码ID)',
  `channel_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '通道编码',
  `monitor_no` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '摄像头编号',
  `scene_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景编码',
  `pics` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件图片',
  `violation_box` text COLLATE utf8mb4_unicode_ci COMMENT '违规框',
  `event_at` datetime DEFAULT NULL COMMENT '事件时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='监控事件';

-- ----------------------------
-- Table structure for aiot_event_statistics
-- ----------------------------
DROP TABLE IF EXISTS `aiot_event_statistics`;
CREATE TABLE `aiot_event_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `scene_code` varchar(32) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '场景编码',
  `event_count` int(11) DEFAULT '0' COMMENT '告警次数',
  `event_date` date DEFAULT NULL COMMENT '事件日期',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `project_id` bigint(20) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1714075292297191425 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事件统计分类配置表';

-- ----------------------------
-- Table structure for aiot_scene
-- ----------------------------
DROP TABLE IF EXISTS `aiot_scene`;
CREATE TABLE `aiot_scene` (
  `id` bigint(20) NOT NULL,
  `name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景名称',
  `code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景代码',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='aiot违规类型场景';

-- ----------------------------
-- Table structure for aiot_scene_category
-- ----------------------------
DROP TABLE IF EXISTS `aiot_scene_category`;
CREATE TABLE `aiot_scene_category` (
  `id` bigint(20) NOT NULL,
  `cate_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景分类代码',
  `scene_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '场景代码',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idex_scene_cate` (`cate_code`,`scene_code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for aiot_scene_mgr
-- ----------------------------
DROP TABLE IF EXISTS `aiot_scene_mgr`;
CREATE TABLE `aiot_scene_mgr` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `p_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '父场景类别id',
  `scene_code` varchar(20) NOT NULL COMMENT '场景类别编号',
  `scene_name` varchar(255) NOT NULL COMMENT '场景类别名称',
  `scene_illustrate` varchar(2000) DEFAULT NULL COMMENT '场景类别说明',
  `whole_ids` varchar(127) DEFAULT NULL COMMENT '场景类别全ids',
  `whole_name` varchar(255) DEFAULT NULL COMMENT '场景类别全称',
  `created_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `created_by_name` varchar(255) DEFAULT NULL COMMENT '创建人姓名',
  `updated_by` bigint(20) DEFAULT NULL COMMENT '修改人',
  `updated_by_name` varchar(255) DEFAULT NULL COMMENT '修改人姓名',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1:删除；0:未删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='场景类别';

-- ----------------------------
-- Table structure for back_ext
-- ----------------------------
DROP TABLE IF EXISTS `back_ext`;
CREATE TABLE `back_ext` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `value` varchar(256) DEFAULT NULL,
  `label` varchar(128) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `act_id` (`account_id`),
  KEY `val_id` (`value`(255))
) ENGINE=InnoDB AUTO_INCREMENT=1694833147064385537 DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for com_counter
-- ----------------------------
DROP TABLE IF EXISTS `com_counter`;
CREATE TABLE `com_counter` (
  `id` bigint(20) unsigned NOT NULL COMMENT '计数器',
  `ref_id` bigint(20) DEFAULT NULL COMMENT '来源id',
  `refer` varchar(32) DEFAULT NULL COMMENT '来源，ncms_articles, cms_comment, ncms_attachment, ncms_category, ncms_specialcolumn, ncms_word_hot, ncms_tags, ncms_word_source, ncms_word_sensitive',
  `counter` varchar(32) DEFAULT NULL COMMENT '计数器类型或叫名称，page,user, upvote, comment, favor, share, read, article, serach',
  `count` int(11) DEFAULT '0' COMMENT '计数值',
  `updated_at` datetime DEFAULT NULL COMMENT '最后操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `ref_id` (`ref_id`,`refer`,`counter`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for com_dictionary
-- ----------------------------
DROP TABLE IF EXISTS `com_dictionary`;
CREATE TABLE `com_dictionary` (
  `id` bigint(20) NOT NULL COMMENT '数据字典',
  `refer` varchar(64) DEFAULT NULL COMMENT '表',
  `refer_name` varchar(64) DEFAULT NULL COMMENT '表名',
  `ref_clo` varchar(64) DEFAULT NULL COMMENT '列',
  `ref_clo_name` varchar(64) DEFAULT NULL COMMENT '列名',
  `p_id` bigint(20) DEFAULT '0' COMMENT '父级节点',
  `name` varchar(64) DEFAULT NULL COMMENT '键',
  `value` varchar(128) DEFAULT NULL COMMENT '值',
  `type` varchar(64) DEFAULT NULL COMMENT '类型',
  `remark` varchar(128) DEFAULT NULL COMMENT '备注',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `enabled` tinyint(4) DEFAULT '1' COMMENT '是否锁定',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for com_org
-- ----------------------------
DROP TABLE IF EXISTS `com_org`;
CREATE TABLE `com_org` (
  `id` bigint(20) NOT NULL COMMENT '组织机构表',
  `brief` varchar(1024) DEFAULT NULL COMMENT '摘要／描述',
  `code` varchar(255) DEFAULT NULL COMMENT '唯一编码',
  `org_id` bigint(20) DEFAULT '0' COMMENT '父级节点',
  `type_id` bigint(20) DEFAULT NULL COMMENT '组织机构编号',
  `title` varchar(255) DEFAULT NULL COMMENT '机构名称',
  `disable` tinyint(4) DEFAULT '0' COMMENT '是否隐藏',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `sort` int(20) DEFAULT '0',
  `p_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `code` (`code`) USING BTREE,
  KEY `org_id` (`org_id`) USING BTREE,
  KEY `title` (`title`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for com_org_user
-- ----------------------------
DROP TABLE IF EXISTS `com_org_user`;
CREATE TABLE `com_org_user` (
  `id` bigint(20) NOT NULL,
  `org_id` bigint(20) DEFAULT NULL COMMENT '组织编号',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `job` varchar(128) DEFAULT NULL COMMENT '职务',
  `sort` int(11) DEFAULT NULL COMMENT '排序值',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `org_id_user_id` (`org_id`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for com_orgtag
-- ----------------------------
DROP TABLE IF EXISTS `com_orgtag`;
CREATE TABLE `com_orgtag` (
  `id` bigint(20) unsigned NOT NULL COMMENT '组织架构的用户角色',
  `p_id` bigint(20) DEFAULT '0' COMMENT '父级id',
  `title` varchar(64) DEFAULT NULL COMMENT '标题',
  `code` varchar(64) DEFAULT NULL COMMENT '唯一识别码',
  `brief` varchar(64) DEFAULT NULL COMMENT '摘要',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `status` tinyint(4) DEFAULT NULL COMMENT '是否有效',
  `sort` int(20) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='人员标签';

-- ----------------------------
-- Table structure for com_orgtag_user
-- ----------------------------
DROP TABLE IF EXISTS `com_orgtag_user`;
CREATE TABLE `com_orgtag_user` (
  `id` bigint(20) unsigned NOT NULL,
  `org_tag_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for com_orgtype
-- ----------------------------
DROP TABLE IF EXISTS `com_orgtype`;
CREATE TABLE `com_orgtype` (
  `id` bigint(20) unsigned NOT NULL COMMENT '组织机构type',
  `brief` varchar(64) DEFAULT NULL COMMENT '摘要',
  `title` varchar(64) DEFAULT NULL COMMENT '显示名称',
  `code` varchar(64) DEFAULT NULL COMMENT '唯一key',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL COMMENT '是否有效',
  `sort` bigint(20) DEFAULT '0',
  `type` varchar(64) DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='组织机构类型';

-- ----------------------------
-- Table structure for com_region
-- ----------------------------
DROP TABLE IF EXISTS `com_region`;
CREATE TABLE `com_region` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `p_id` bigint(20) NOT NULL DEFAULT '1' COMMENT '父级ID',
  `type` varchar(64) CHARACTER SET utf8 DEFAULT '' COMMENT '类型， province, city, district, subdistrict, community, neighborhood, residential community',
  `title` varchar(256) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称',
  `spell` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称拼音',
  `spell_short` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '名称拼音首字母',
  `brief` varchar(512) CHARACTER SET utf8 DEFAULT NULL COMMENT '简述',
  `fence_id` text COMMENT '区域块点位id,多个用逗号分隔',
  `cover` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '图片',
  `content` text CHARACTER SET utf8 COMMENT '内容',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '图片资源编号',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(4) DEFAULT NULL COMMENT '是否开启',
  `pid` varchar(255) CHARACTER SET utf8 DEFAULT NULL COMMENT '父id',
  `sort` int(20) DEFAULT '0' COMMENT '排序',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '经纬度表id (gis_poi)',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `gd_id` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '行政区划在高德的id',
  `gd_adcode` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '行政区划在高德的adcode',
  `district_id` varchar(20) CHARACTER SET utf8 DEFAULT NULL COMMENT 'districtId',
  `region_code` bigint(16) DEFAULT NULL COMMENT '行政区划编码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `deleted_at` (`deleted_at`) USING BTREE,
  KEY `pid_title` (`p_id`,`title`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1656394300069187585 DEFAULT CHARSET=utf8mb4 COMMENT='通用地区';

-- ----------------------------
-- Table structure for com_upload_resource
-- ----------------------------
DROP TABLE IF EXISTS `com_upload_resource`;
CREATE TABLE `com_upload_resource` (
  `id` bigint(20) NOT NULL,
  `userid` int(11) DEFAULT NULL COMMENT '用户ID',
  `name` varchar(128) DEFAULT NULL COMMENT '文件名',
  `path` varchar(1024) DEFAULT NULL COMMENT '文件相对路径',
  `type` varchar(32) DEFAULT NULL COMMENT 'mime类型',
  `size` int(11) DEFAULT NULL COMMENT '文件大小',
  `file_name` varchar(128) DEFAULT NULL COMMENT '原文件名',
  `create_time` datetime DEFAULT NULL,
  `create_ip` varchar(16) DEFAULT NULL,
  `remote_url` varchar(1024) DEFAULT NULL COMMENT '文件绝对路径',
  `local_url` varchar(1024) DEFAULT NULL COMMENT '保存在服务器的文件路径',
  `remark` varchar(1024) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `memo` varchar(1024) DEFAULT NULL,
  `user_id` int(11) DEFAULT NULL,
  `thumb` varchar(1024) DEFAULT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `type_id` int(11) DEFAULT NULL,
  `account_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `file_name` (`name`) USING BTREE,
  KEY `local_url` (`local_url`(255)) USING BTREE,
  KEY `path` (`path`(255)) USING BTREE,
  KEY `remote_url` (`remote_url`(255)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for competition_analysis_key_word
-- ----------------------------
DROP TABLE IF EXISTS `competition_analysis_key_word`;
CREATE TABLE `competition_analysis_key_word` (
  `id` bigint(20) NOT NULL,
  `detecting_key_word` varchar(48) DEFAULT NULL COMMENT '检测关键词',
  `correct_words` varchar(255) DEFAULT NULL COMMENT '合规正确词（多个词用 、分隔）',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态',
  `meno` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='内容差错监控关键词库';

-- ----------------------------
-- Table structure for construction_unit
-- ----------------------------
DROP TABLE IF EXISTS `construction_unit`;
CREATE TABLE `construction_unit` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '施工单位名称',
  `admin_user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员用户ID，关联移动端认证的管理员',
  `admin_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员姓名',
  `admin_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员联系方式',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `updated_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB AUTO_INCREMENT=1711967473853231106 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施工单位表';

-- ----------------------------
-- Table structure for construction_unit_organization_relate
-- ----------------------------
DROP TABLE IF EXISTS `construction_unit_organization_relate`;
CREATE TABLE `construction_unit_organization_relate` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `construction_unit_id` bigint(20) NOT NULL COMMENT '施工单位ID',
  `organization_id` bigint(20) NOT NULL COMMENT '组织ID',
  `credit_score` decimal(5,2) DEFAULT '100.00' COMMENT '信用分数，每个组织独立计算',
  `is_blacklist` tinyint(1) DEFAULT '0' COMMENT '是否黑名单：0-否，1-是',
  `blacklist_reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '黑名单原因',
  `blacklist_time` datetime DEFAULT NULL COMMENT '加入黑名单时间',
  `contractor_charger` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人',
  `contractor_charger_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人电话',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `updated_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_unit_org` (`construction_unit_id`,`organization_id`),
  KEY `idx_construction_unit_id` (`construction_unit_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_is_blacklist` (`is_blacklist`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施工单位组织关联表';

-- ----------------------------
-- Table structure for contracting_unit
-- ----------------------------
DROP TABLE IF EXISTS `contracting_unit`;
CREATE TABLE `contracting_unit` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '施工单位名称',
  `admin_user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员用户ID，关联移动端认证的管理员',
  `admin_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员姓名',
  `admin_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '管理员联系方式',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `updated_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_admin_user_id` (`admin_user_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施工单位表';

-- ----------------------------
-- Table structure for contracting_unit_organization_relate
-- ----------------------------
DROP TABLE IF EXISTS `contracting_unit_organization_relate`;
CREATE TABLE `contracting_unit_organization_relate` (
  `id` bigint(20) NOT NULL COMMENT '主键ID',
  `contracting_unit_id` bigint(20) NOT NULL COMMENT '施工单位ID',
  `organization_id` bigint(20) NOT NULL COMMENT '组织ID',
  `credit_score` decimal(5,2) DEFAULT '100.00' COMMENT '信用分数，每个组织独立计算',
  `is_blacklist` tinyint(1) DEFAULT '0' COMMENT '是否黑名单：0-否，1-是',
  `blacklist_reason` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '黑名单原因',
  `blacklist_time` datetime DEFAULT NULL COMMENT '加入黑名单时间',
  `leader` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人',
  `leader_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人电话',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建人',
  `updated_user` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_unit_org` (`contracting_unit_id`,`organization_id`),
  KEY `idx_contracting_unit_id` (`contracting_unit_id`),
  KEY `idx_organization_id` (`organization_id`),
  KEY `idx_is_blacklist` (`is_blacklist`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_is_deleted` (`is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='施工单位组织关联表';

-- ----------------------------
-- Table structure for device_camera_config
-- ----------------------------
DROP TABLE IF EXISTS `device_camera_config`;
CREATE TABLE `device_camera_config` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `type_id` bigint(20) NOT NULL COMMENT '类型id device_camera_type',
  `camera_type` tinyint(4) NOT NULL COMMENT '设备类型 event_camera_point 的 type',
  `host` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '域名 host (必填)',
  `app_key` varchar(127) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'appkey (必填)',
  `app_secret` varchar(127) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'appsecret (必填)',
  `project_id` bigint(31) DEFAULT NULL COMMENT 'projectid (gcca使用)',
  `user_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户名',
  `user_pwd` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '密码',
  `is_deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标记 1-已删除 0-默认',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='摄像头配置表';

-- ----------------------------
-- Table structure for device_camera_type
-- ----------------------------
DROP TABLE IF EXISTS `device_camera_type`;
CREATE TABLE `device_camera_type` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `type` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型名称',
  `is_deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标记 1-已删除 0-默认',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='摄像头类型表';

-- ----------------------------
-- Table structure for engineering_members
-- ----------------------------
DROP TABLE IF EXISTS `engineering_members`;
CREATE TABLE `engineering_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) DEFAULT '0' COMMENT '用户id',
  `realname` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '成员姓名',
  `mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `type` int(8) DEFAULT NULL COMMENT '职位类型',
  `id_card` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `work_type_name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工种名称',
  `certificate_number` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书编号',
  `certificate_start_date` date DEFAULT NULL COMMENT '证书有效日期-开始时间',
  `certificate_end_date` date DEFAULT NULL COMMENT '证书有效日期-结束时间',
  `certificate_pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '证书图片',
  `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `status` tinyint(4) DEFAULT '1' COMMENT '有效状态，0-无效，1-有效',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE,
  KEY `idx_deletedat_realname_userid` (`is_deleted`,`realname`,`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713775479056621569 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-工程人员';

-- ----------------------------
-- Table structure for engineering_members_project_relate
-- ----------------------------
DROP TABLE IF EXISTS `engineering_members_project_relate`;
CREATE TABLE `engineering_members_project_relate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` bigint(20) DEFAULT NULL COMMENT '成员id，关联engineering_members',
  `relate_id` bigint(20) DEFAULT NULL COMMENT 'relateId，engineering_members_relate',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id,关联xsgc_organization',
  `project_id` bigint(20) DEFAULT NULL COMMENT '项目id,关联sporadic_project',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1713788653893730305 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-工程人员关联项目表';

-- ----------------------------
-- Table structure for engineering_members_type
-- ----------------------------
DROP TABLE IF EXISTS `engineering_members_type`;
CREATE TABLE `engineering_members_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '职位名称',
  `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-工程人员职位';

-- ----------------------------
-- Table structure for event_camera_point
-- ----------------------------
DROP TABLE IF EXISTS `event_camera_point`;
CREATE TABLE `event_camera_point` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `device_id` bigint(20) DEFAULT NULL COMMENT 'device_id',
  `serial_no` varchar(63) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '序列号',
  `monitor_no` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备号',
  `flv_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'FLV协议播放地址（流畅）',
  `hd_flv_address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'FLV协议直播地址（高清）',
  `model_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监控设备型号',
  `project_id` bigint(20) DEFAULT NULL COMMENT '小散项目id',
  `perception_types` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '云端感知，边缘感知，AI盒子，逗号隔开',
  `voice_broadcast` tinyint(4) DEFAULT NULL COMMENT '是否语音播报，0-不是，1-是',
  `smart` tinyint(4) DEFAULT NULL COMMENT '是否智能，0-不是，1-是',
  `type` tinyint(4) DEFAULT NULL COMMENT '类型，1-自建，2-环卫云，3-1078，4-萤石云，5-车载视频API, 6-锐明, 7-海康车载视频API, 8-政务云, 9-大华云 10-鲲云云 11-平安云 12-旭感云 13-GB28181',
  `status` tinyint(4) DEFAULT '0' COMMENT '在线状态，0-离线，1-在线',
  `real_status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '真实状态 0-离线 1-在线',
  `open_live` tinyint(4) DEFAULT '0' COMMENT '直播状态，0-未使用或直播已关闭，1-使用中，2-已过期，3-直播已暂停',
  `sip_user_id` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国标视频编码',
  `channel` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '通道号',
  `memo` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
  `online_at` datetime DEFAULT NULL COMMENT '在线时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `card_id` bigint(20) DEFAULT NULL COMMENT '物联网卡id',
  `stock_status` tinyint(4) DEFAULT NULL COMMENT '出入库状态：1-已入库，2-已出库',
  `is_lock` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否锁定 0-正常 1-锁定',
  `is_healthy` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否健康 0-异常 1-健康',
  `original_serial_no` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始序列号',
  `monitor_name` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '通道名称',
  `channel_id` bigint(20) DEFAULT '0' COMMENT '通道id',
  `real_online_at` datetime DEFAULT NULL COMMENT '真实在线时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `monitor_no` (`monitor_no`) USING BTREE,
  KEY `serial_no` (`serial_no`) USING BTREE,
  KEY `area_id` (`project_id`) USING BTREE,
  KEY `card_id` (`card_id`) USING BTREE,
  KEY `model_no` (`model_no`) USING BTREE,
  KEY `device_id` (`device_id`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='摄像头信息表';

-- ----------------------------
-- Table structure for event_camera_point_device
-- ----------------------------
DROP TABLE IF EXISTS `event_camera_point_device`;
CREATE TABLE `event_camera_point_device` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `serial_no` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '序列号',
  `sip_user_id` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '国标视频编码',
  `project_id` bigint(20) DEFAULT NULL COMMENT '工程id（小散工程表）',
  `model_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监控设备型号',
  `type` tinyint(4) NOT NULL COMMENT '类型 event_camera_point_type',
  `perception_types` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '云端感知，边缘感知，AI盒子，逗号隔开',
  `voice_broadcast` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否语音播报，0-不是，1-是',
  `is_healthy` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否健康 0-异常 1-健康',
  `smart` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否智能，0-不是，1-是',
  `memo` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='摄像头表';

-- ----------------------------
-- Table structure for event_camera_point_type
-- ----------------------------
DROP TABLE IF EXISTS `event_camera_point_type`;
CREATE TABLE `event_camera_point_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` tinyint(4) DEFAULT NULL COMMENT 'code',
  `name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型名',
  `is_point` tinyint(4) NOT NULL DEFAULT '0' COMMENT '投放点 0-否 1-是',
  `is_station` tinyint(4) NOT NULL COMMENT '基地',
  `is_vehicle` tinyint(4) NOT NULL COMMENT '车辆',
  `is_temp_point` tinyint(4) NOT NULL COMMENT '暂存点',
  `is_open` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否第三方 0-否 1-是',
  `is_rebirth` tinyint(4) NOT NULL DEFAULT '0' COMMENT '再生资源',
  `is_used` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否在用 0-否 1-是',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0-否 1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1327972406192115789 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='开放 ai 摄像头类型表';

-- ----------------------------
-- Table structure for event_model_type_config
-- ----------------------------
DROP TABLE IF EXISTS `event_model_type_config`;
CREATE TABLE `event_model_type_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sc_model_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '深传设备型号',
  `hk_model_no` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '海康设备型号',
  `pic` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片,在线图标',
  `off_line_pic` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '离线图标',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除，1-删除，0-未删除',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1196255075958538241 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='设备型号配置表';

-- ----------------------------
-- Table structure for event_order
-- ----------------------------
DROP TABLE IF EXISTS `event_order`;
CREATE TABLE `event_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工单ID',
  `region_pid` bigint(20) DEFAULT NULL COMMENT '辖区id',
  `region_id` bigint(20) DEFAULT NULL COMMENT '街道id',
  `region_cid` bigint(20) DEFAULT NULL COMMENT '社区id',
  `project_id` bigint(20) DEFAULT NULL COMMENT '项目id',
  `camera_no` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '摄像头编号',
  `event_code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件编号',
  `event_source` tinyint(4) DEFAULT NULL COMMENT '事件来源 1-海康 2-海康服务器 3-鲲云 4-深传 5-大华 6-自研分析 7-第三方上报',
  `event_desc` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件描述',
  `pic` varchar(1023) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片url列表',
  `disposing_pic` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理时的摄像头截图',
  `type_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '违规类型编号',
  `type_name` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '违规类型名称',
  `duration_time` int(11) DEFAULT '0' COMMENT '持续时间/秒',
  `duration_time_str` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '持续时间字符串',
  `company_id` bigint(20) DEFAULT NULL COMMENT '所属运营公司id',
  `origin` tinyint(4) DEFAULT NULL COMMENT '来源，1-智能分析 2-督导上报',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态，0-待处理，1-已处理，2-超时自动结束， 3-待复核， 4-已复核， 5-任务流转',
  `review_status` tinyint(4) DEFAULT NULL COMMENT '复核状态，1-提交整改，2-识别有误',
  `is_assign` tinyint(4) DEFAULT '0' COMMENT '是否手动分派，0-否，1-是',
  `assign_company_id` bigint(20) DEFAULT NULL COMMENT '最后指派到的运营公司id',
  `assign_user_id` bigint(20) DEFAULT NULL COMMENT '最后指派人id',
  `assigned_at` datetime DEFAULT NULL COMMENT '最后指派时间',
  `dispose_type` tinyint(4) DEFAULT '1' COMMENT '处理人角色类型，1-运营公司人员，2-超级管理员',
  `dispose_desc` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理意见',
  `dispose_pic` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理图片id列表',
  `member_id` bigint(20) DEFAULT NULL COMMENT '处理人id， event_company_member表id',
  `reviewer_id` bigint(20) DEFAULT NULL COMMENT '复核人id， sys_users表id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '超级管理员处理人id',
  `dispose_name` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '处理人姓名 (第三方) ',
  `memo` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `violation_areas` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '违规图片范围',
  `is_todo` tinyint(4) DEFAULT '0' COMMENT '是否推送统一代办成功，0-否，1-是',
  `disposed_at` datetime DEFAULT NULL COMMENT '处理时间',
  `reviewed_at` datetime DEFAULT NULL COMMENT '复核时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除，0-未删除，1-删除',
  `event_latest_at` datetime DEFAULT NULL COMMENT '事件最后时间',
  `evented_at` datetime DEFAULT NULL COMMENT '事件时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '结束时间',
  `is_rule_source` tinyint(4) DEFAULT '0' COMMENT '是否违规溯源，0-否，1-是',
  `source_pic` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '违规溯源图片',
  `source_video` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '违规溯源视频',
  `type` tinyint(4) DEFAULT '0' COMMENT '场所类型 0-未知 1-中转站 2-处置基地 (中转站服务在用，深分类没有)',
  `video_url` varchar(516) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '违规视频地址',
  `is_report` tinyint(4) DEFAULT '0' COMMENT '是否已上报全周期平台0-否，1-是',
  `is_city_stat` tinyint(4) DEFAULT '0' COMMENT '是否已上报市级平台（在市级事件列表展示和AI建设率中统计）。0-否；1-是',
  `municipal_place_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '市级场所编码',
  `municipal_point_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '市级投放点编码',
  `timeout_case` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '超时情况（多个英文逗号隔开）  1=一级处理人超时 2=二级处理人超时 3=三级处理人超时',
  `is_on_time` tinyint(4) DEFAULT '0' COMMENT '是否及时处理:0=否、1=是 ',
  `check_status` tinyint(4) DEFAULT NULL COMMENT '是否推送',
  `alarm_pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `is_push` tinyint(4) DEFAULT NULL,
  `error_id` bigint(20) DEFAULT NULL COMMENT '福田：错误配置id 对应error_config表',
  `sync_at` datetime DEFAULT NULL COMMENT '同步入库时间',
  `data_source_code` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '数据来源',
  PRIMARY KEY (`id`),
  KEY `region_pid` (`region_pid`),
  KEY `region_id` (`region_id`),
  KEY `region_cid` (`region_cid`),
  KEY `project_id` (`project_id`),
  KEY `dispose` (`dispose_desc`),
  KEY `status` (`status`),
  KEY `camera_no` (`camera_no`),
  KEY `event_code` (`event_code`),
  KEY `evented_at` (`evented_at`,`is_deleted`) USING BTREE,
  KEY `type_code_idx` (`is_deleted`,`type_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1703166133689806849 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事件工单表';

-- ----------------------------
-- Table structure for event_order_source
-- ----------------------------
DROP TABLE IF EXISTS `event_order_source`;
CREATE TABLE `event_order_source` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `code` int(31) DEFAULT NULL COMMENT '事件来源code',
  `name` varchar(25) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '事件来源名称',
  `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否有效  0-无效 1-有效',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否删除 0-未删除 1-已删除',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='事件来源表';

-- ----------------------------
-- Table structure for event_order_type
-- ----------------------------
DROP TABLE IF EXISTS `event_order_type`;
CREATE TABLE `event_order_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键自增',
  `title` varchar(63) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '事件名称',
  `sort` int(8) NOT NULL DEFAULT '0' COMMENT '排序',
  `is_self` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否事件枚举 0-否 1-是',
  `is_open` tinyint(3) NOT NULL DEFAULT '0' COMMENT '是否第三方事件枚举 0-否 1-是',
  `is_deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标记 1-已删除 0-默认',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=90010 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='监控事件类型';

-- ----------------------------
-- Table structure for file_upload_resource
-- ----------------------------
DROP TABLE IF EXISTS `file_upload_resource`;
CREATE TABLE `file_upload_resource` (
  `id` bigint(20) unsigned NOT NULL,
  `userid` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `name` varchar(1024) DEFAULT NULL COMMENT '文件名',
  `path` varchar(1024) DEFAULT NULL COMMENT '文件相对路径',
  `type` varchar(128) DEFAULT NULL COMMENT 'mime类型',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `file_name` varchar(1024) DEFAULT NULL COMMENT '原文件名',
  `file_length` bigint(20) DEFAULT NULL COMMENT '媒体文件时长',
  `storage_type` tinyint(4) DEFAULT NULL COMMENT '存储类型: 0. oss 1. minio 2. 外部资源',
  `relative_path` varchar(1024) DEFAULT NULL COMMENT '文件相对路径',
  `access_control` tinyint(4) DEFAULT NULL COMMENT '访问权限: 0. oss 1. minio',
  `remote_url` varchar(1024) DEFAULT NULL COMMENT '文件绝对路径',
  `local_url` varchar(1024) DEFAULT NULL COMMENT '保存在服务器的文件路径',
  `minio_url` varchar(1024) DEFAULT NULL COMMENT '迁移后保存至minio的文件路径',
  `remark` varchar(1024) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `memo` varchar(1024) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '上传者id',
  `thumb` varchar(1024) DEFAULT NULL,
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id',
  `type_id` int(11) DEFAULT NULL,
  `create_ip` varchar(16) DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `signature` varchar(64) DEFAULT NULL COMMENT '服务端签名',
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `oss_url` varchar(1024) DEFAULT NULL COMMENT '按租户隔离后迁移至oss的url',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `file_name` (`name`) USING BTREE,
  KEY `path` (`path`(255)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for fn_rmsv3_icon_menu
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv3_icon_menu`;
CREATE TABLE `fn_rmsv3_icon_menu` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `platform` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT 'mini' COMMENT 'mini-小程序，h5-网页，app-手机客户端',
  `group_key` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分组',
  `group_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单分组名称',
  `label` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单标签',
  `link` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单路由',
  `link_id` bigint(20) DEFAULT NULL COMMENT 'fn_rmsv_route_config',
  `icon` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '菜单图标',
  `resource_id` bigint(20) DEFAULT NULL COMMENT 'fn_rmsv3_resource',
  `type` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'icon类型:url,event,page',
  `is_tab` tinyint(4) DEFAULT '0' COMMENT '是否tab页，0-否，1-是',
  `badge` varchar(32) CHARACTER SET utf16 DEFAULT NULL COMMENT '徽章',
  `sort` tinyint(4) DEFAULT '0' COMMENT '排序值',
  `permit` tinyint(4) DEFAULT '0' COMMENT '是否有权限，1-是，0-否',
  `active` tinyint(4) DEFAULT '1' COMMENT '是否启用',
  `test` tinyint(4) DEFAULT '0' COMMENT '是否是灰度测试',
  `test_role` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '允许查看灰度测试菜单的角色 sys_roles',
  `memo` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `is_share` tinyint(4) DEFAULT '0' COMMENT '是否分享0否1是',
  `origin` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `search` tinyint(4) DEFAULT '0' COMMENT '是否开启搜索',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `group` (`group_key`),
  KEY `sort` (`sort`)
) ENGINE=InnoDB AUTO_INCREMENT=1713312002411905025 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ----------------------------
-- Table structure for fn_rmsv3_members
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv3_members`;
CREATE TABLE `fn_rmsv3_members` (
  `id` bigint(22) NOT NULL COMMENT 'id',
  `user_id` bigint(22) DEFAULT NULL COMMENT '用户id',
  `realname` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '姓名',
  `mobile` varchar(13) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号',
  `organization_id` bigint(22) DEFAULT NULL COMMENT '组织id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-组织团队成员表';

-- ----------------------------
-- Table structure for fn_rmsv3_members_type
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv3_members_type`;
CREATE TABLE `fn_rmsv3_members_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(64) CHARACTER SET utf8 DEFAULT NULL COMMENT '类型名称',
  `memo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-组织团队成员类型';

-- ----------------------------
-- Table structure for fn_rmsv3_members_type_relate
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv3_members_type_relate`;
CREATE TABLE `fn_rmsv3_members_type_relate` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `member_id` bigint(20) NOT NULL COMMENT '成员id',
  `level` tinyint(4) DEFAULT NULL COMMENT '级别，0-市级 1-区级 2-街道级 3-社区级',
  `type_id` bigint(20) NOT NULL COMMENT '类型id (字典表：fn_rmsv3_members_type)',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `active` tinyint(4) DEFAULT '1' COMMENT '是否有效 0-无效 1-有效',
  `pic` varchar(255) DEFAULT NULL COMMENT '照片id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='小散工程-组织团队成员关系';

-- ----------------------------
-- Table structure for fn_rmsv3_members_type_relate_binding
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv3_members_type_relate_binding`;
CREATE TABLE `fn_rmsv3_members_type_relate_binding` (
  `id` bigint(20) NOT NULL COMMENT '主键id',
  `relate_id` bigint(20) DEFAULT NULL COMMENT 'fn_rmsv3_members_type_relate id',
  `region_pid` bigint(20) DEFAULT NULL COMMENT '区',
  `region_id` bigint(20) DEFAULT NULL COMMENT '街道',
  `region_cid` bigint(20) DEFAULT NULL COMMENT '社区',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_region_pid` (`region_pid`,`region_id`,`region_cid`) USING BTREE,
  KEY `idx_relate_id` (`relate_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='小散工程-组织团队成员区域关联关系';

-- ----------------------------
-- Table structure for fn_rmsv3_resource
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv3_resource`;
CREATE TABLE `fn_rmsv3_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `url` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源链接',
  `name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源名称',
  `menu_names` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '关联的菜单名称列表',
  `memo` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `type` tinyint(4) DEFAULT NULL COMMENT '类型，1-图片，2-文件，3-视频',
  `origin` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '测试服ID',
  `code` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '唯一标识',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除，0-未删除，1-已删除',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`type`,`code`)
) ENGINE=InnoDB AUTO_INCREMENT=1713378645548597249 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='资源表';

-- ----------------------------
-- Table structure for fn_rmsv_route_config
-- ----------------------------
DROP TABLE IF EXISTS `fn_rmsv_route_config`;
CREATE TABLE `fn_rmsv_route_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `platform` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'mini' COMMENT '平台：mini-小程序，h5-网页，app-手机客户端',
  `app_group` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用分组',
  `app_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '应用名称',
  `link` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '链接',
  `authorization` tinyint(4) DEFAULT '0' COMMENT '是否授权0否1是',
  `picture` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '二维码图片',
  `memo` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '路由说明',
  `icon` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标信息暂时不使用',
  `phone_capture` varchar(2048) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机截图',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `banner_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '轮播图',
  `icon_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图标名称',
  `is_share` tinyint(4) DEFAULT '0' COMMENT '是否分享0否1是',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `group` (`app_group`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713750682427506689 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='路由配置表';

-- ----------------------------
-- Table structure for gis_fence
-- ----------------------------
DROP TABLE IF EXISTS `gis_fence`;
CREATE TABLE `gis_fence` (
  `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '区域块/线路',
  `type` tinyint(4) DEFAULT NULL COMMENT '类型 0.线路 1.圆 2.多边形',
  `points` varchar(5000) DEFAULT NULL COMMENT '经纬度集合经纬度之间用逗号隔开，一组经纬度之间用分号隔开',
  `lng` varchar(64) DEFAULT NULL COMMENT '圆心经度',
  `lat` varchar(64) DEFAULT NULL COMMENT '圆心纬度',
  `radius` int(16) DEFAULT NULL COMMENT '圆形围栏半径（米）',
  `name` varchar(265) DEFAULT NULL COMMENT '名称',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `valid_time` datetime DEFAULT NULL COMMENT '有效时间',
  `memo` varchar(255) DEFAULT NULL COMMENT '备注',
  `area` float DEFAULT NULL COMMENT '面积',
  `author` varchar(12) DEFAULT NULL COMMENT '数据添加者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for gis_poi
-- ----------------------------
DROP TABLE IF EXISTS `gis_poi`;
CREATE TABLE `gis_poi` (
  `id` bigint(20) NOT NULL DEFAULT '0' COMMENT '区域块/线路',
  `lat` varchar(32) DEFAULT NULL COMMENT '纬度',
  `lng` varchar(32) DEFAULT NULL COMMENT '经度',
  `addr` varchar(255) DEFAULT NULL COMMENT '地址',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `valid_time` datetime DEFAULT NULL COMMENT '有效时间',
  `author` varchar(12) DEFAULT NULL COMMENT '数据添加者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for monitor_access_info
-- ----------------------------
DROP TABLE IF EXISTS `monitor_access_info`;
CREATE TABLE `monitor_access_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `device_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备id，关联event_camera_point_device表',
  `serial_no` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监控序列号',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713367742102986753 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程-监控接入详情';

-- ----------------------------
-- Table structure for monitor_flow
-- ----------------------------
DROP TABLE IF EXISTS `monitor_flow`;
CREATE TABLE `monitor_flow` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `project_id` bigint(20) DEFAULT NULL COMMENT '项目id,关联sporadic_project',
  `relate_id` bigint(20) DEFAULT NULL COMMENT '详情id ，关联',
  `flow` tinyint(4) DEFAULT '0' COMMENT '当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-监管结束',
  `state` tinyint(4) DEFAULT '0' COMMENT '当前节点状态，0-未完成，1-已完成',
  `finish_time` datetime DEFAULT NULL COMMENT '当前节点结束时间',
  `is_deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标记 1-已删除 0-默认',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713883659379269633 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程';

-- ----------------------------
-- Table structure for monitor_install
-- ----------------------------
DROP TABLE IF EXISTS `monitor_install`;
CREATE TABLE `monitor_install` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `contact_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人电话',
  `contact_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `memo` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '安装说明',
  `pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '现场图片',
  `install_time` datetime DEFAULT NULL COMMENT '安装时间',
  `install_cnt` int(10) DEFAULT '0' COMMENT '安装的监控数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713364859131355137 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程-上门安装详情';

-- ----------------------------
-- Table structure for monitor_install_reservation
-- ----------------------------
DROP TABLE IF EXISTS `monitor_install_reservation`;
CREATE TABLE `monitor_install_reservation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `contact_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人电话',
  `contact_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `reservation_time` datetime DEFAULT NULL COMMENT '预约时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713363586424332289 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程-安装预约详情';

-- ----------------------------
-- Table structure for monitor_on_scene_inspection
-- ----------------------------
DROP TABLE IF EXISTS `monitor_on_scene_inspection`;
CREATE TABLE `monitor_on_scene_inspection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `contact_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人电话',
  `contact_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `memo` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '勘察说明',
  `pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '现场图片',
  `inspect_time` datetime DEFAULT NULL COMMENT '勘察时间',
  `install_cnt` int(10) DEFAULT '0' COMMENT '评估需要安装的监控数',
  `reservation_time` datetime DEFAULT NULL COMMENT '预约安装时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713364148595286017 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程-现场勘察详情';

-- ----------------------------
-- Table structure for monitor_order
-- ----------------------------
DROP TABLE IF EXISTS `monitor_order`;
CREATE TABLE `monitor_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `project_id` bigint(20) DEFAULT NULL COMMENT '项目id',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `region_pid` bigint(20) DEFAULT NULL COMMENT '区id',
  `region_id` bigint(20) DEFAULT NULL COMMENT '街道id',
  `region_cid` bigint(20) DEFAULT NULL COMMENT '社区id',
  `flow` tinyint(4) DEFAULT '0' COMMENT '当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-监管结束',
  `flow_id` bigint(4) DEFAULT NULL COMMENT '当前流程id,关联monitor_flow表',
  `sort` int(8) DEFAULT '0' COMMENT '自定义的排序字段',
  `is_deleted` tinyint(2) NOT NULL DEFAULT '0' COMMENT '删除标记 1-已删除 0-默认',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713788653956644865 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单';

-- ----------------------------
-- Table structure for monitor_recycle
-- ----------------------------
DROP TABLE IF EXISTS `monitor_recycle`;
CREATE TABLE `monitor_recycle` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `contact_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人电话',
  `contact_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `memo` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '安装说明',
  `pic` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '现场图片',
  `recycle_time` datetime DEFAULT NULL COMMENT '回收时间',
  `recycle_cnt` int(10) DEFAULT '0' COMMENT '回收的监控数',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713371539495804929 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程-上门回收详情';

-- ----------------------------
-- Table structure for monitor_recycle_reservation
-- ----------------------------
DROP TABLE IF EXISTS `monitor_recycle_reservation`;
CREATE TABLE `monitor_recycle_reservation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` bigint(20) DEFAULT NULL COMMENT '工单id，关联monitor_order',
  `contact_mobile` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人电话',
  `contact_name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人姓名',
  `reservation_time` datetime DEFAULT NULL COMMENT '预约时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713380268914085889 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='小散工程-监管工单流程-回收预约详情';

-- ----------------------------
-- Table structure for ncms_article_special
-- ----------------------------
DROP TABLE IF EXISTS `ncms_article_special`;
CREATE TABLE `ncms_article_special` (
  `id` bigint(20) unsigned NOT NULL COMMENT '文章与专题关联表',
  `article_id` bigint(20) DEFAULT NULL COMMENT '文章ID',
  `special_id` bigint(20) DEFAULT NULL COMMENT '专题ID',
  `created_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `article_id2` (`article_id`,`special_id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `category_id` (`special_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_article_tags
-- ----------------------------
DROP TABLE IF EXISTS `ncms_article_tags`;
CREATE TABLE `ncms_article_tags` (
  `id` bigint(20) NOT NULL,
  `article_id` bigint(20) NOT NULL COMMENT '文章ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `article_id_2` (`article_id`,`tag_id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_articles
-- ----------------------------
DROP TABLE IF EXISTS `ncms_articles`;
CREATE TABLE `ncms_articles` (
  `id` bigint(20) unsigned NOT NULL COMMENT '文章id',
  `type` tinyint(4) DEFAULT '1' COMMENT '文章类型,0内容图文,1链接图文,2坐标图文',
  `category_id` bigint(20) DEFAULT NULL,
  `category_ids` varchar(128) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '发布者',
  `source_id` bigint(20) DEFAULT NULL COMMENT '来源',
  `author` varchar(128) DEFAULT NULL COMMENT '作者',
  `title` varchar(1024) DEFAULT NULL COMMENT '文章标题',
  `digest` varchar(1024) DEFAULT NULL COMMENT '文章摘要',
  `picture` varchar(1024) DEFAULT NULL COMMENT '封面',
  `content` longtext COMMENT '文章内容',
  `active` tinyint(4) DEFAULT '1' COMMENT '状态   0不显示  1显示',
  `last_viewed` datetime DEFAULT NULL COMMENT '最后访问时间',
  `refer_url` varchar(1024) DEFAULT NULL COMMENT '关联链接',
  `is_prime` tinyint(4) DEFAULT '0' COMMENT '精华帖,1是,0不是,',
  `anonymous` tinyint(4) DEFAULT '0' COMMENT '是否匿名 1是 0不是',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `address` varchar(256) DEFAULT NULL COMMENT '地址',
  `status` tinyint(4) DEFAULT '0' COMMENT '审核状态0.待审核 1.已审核 2.退回 3.已归档 4.出档 5.审核中 6.已发布 7.草稿',
  `sort` int(11) DEFAULT NULL,
  `is_trend` tinyint(4) DEFAULT '0' COMMENT '热点 1:是 0否',
  `is_topped` tinyint(4) DEFAULT '0' COMMENT '置顶：0否，1是',
  `is_original` tinyint(4) DEFAULT '0' COMMENT '是否原创：0否，1是',
  `is_recommend` int(4) DEFAULT NULL COMMENT '推荐，默认0',
  `is_share` tinyint(4) DEFAULT '0' COMMENT '是否允许共享，1允许，0不允许',
  `is_show_cover` tinyint(4) DEFAULT '0' COMMENT '封面是否显示于正文',
  `share_type` tinyint(4) DEFAULT NULL COMMENT '共享类型，0未共享，1内部，2区内',
  `memo` varchar(255) DEFAULT NULL,
  `cids` varchar(255) DEFAULT NULL,
  `mask_id` varchar(255) DEFAULT NULL,
  `platform_id` bigint(20) DEFAULT NULL COMMENT '发布之后，同步至平台端的id',
  `main_id` bigint(20) DEFAULT NULL COMMENT '发布主体 id',
  `created_ip` varchar(128) DEFAULT NULL COMMENT '创建IP',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `release_at` datetime DEFAULT NULL COMMENT '发布时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最近更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for ncms_articles_participate
-- ----------------------------
DROP TABLE IF EXISTS `ncms_articles_participate`;
CREATE TABLE `ncms_articles_participate` (
  `id` bigint(20) NOT NULL COMMENT '记录查看过文章的用户',
  `user_id` bigint(20) DEFAULT NULL,
  `article_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for ncms_attachment
-- ----------------------------
DROP TABLE IF EXISTS `ncms_attachment`;
CREATE TABLE `ncms_attachment` (
  `id` bigint(20) unsigned NOT NULL COMMENT '文章图片',
  `user_id` bigint(20) DEFAULT NULL,
  `article_id` bigint(20) DEFAULT NULL COMMENT '文章id',
  `type` enum('ARTICLE','INTERACTION') DEFAULT NULL COMMENT '类型，文章类型，article，评论(互动)类型，interaction',
  `file_id` bigint(20) DEFAULT NULL COMMENT '文件Id，可用来下载照片文件',
  `file_name` varchar(64) DEFAULT NULL COMMENT '照片名称',
  `file_url` varchar(255) DEFAULT NULL COMMENT '文件路径url（云储备上获取）',
  `status` tinyint(4) DEFAULT '1' COMMENT '状态 1显示，0隐藏',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `file_id` (`file_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_category
-- ----------------------------
DROP TABLE IF EXISTS `ncms_category`;
CREATE TABLE `ncms_category` (
  `id` bigint(20) unsigned NOT NULL COMMENT '栏目说明',
  `pid` bigint(20) DEFAULT '0' COMMENT '父级版块，默认0',
  `pids` varchar(128) DEFAULT NULL,
  `display_name` varchar(64) DEFAULT NULL COMMENT '版块名称',
  `name` varchar(64) DEFAULT NULL COMMENT '版块的英文名，全表唯一，在前台的URL中进行拼接',
  `logo` varchar(256) DEFAULT NULL COMMENT 'logo，栏目内容图',
  `description` varchar(255) DEFAULT NULL COMMENT '栏目说明',
  `active` tinyint(4) DEFAULT '0' COMMENT '激活状态，默认激活，0否，1是',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `url` varchar(255) DEFAULT NULL COMMENT 'url',
  `permission_id` bigint(11) DEFAULT NULL COMMENT '权限id',
  `icon` varchar(255) DEFAULT NULL COMMENT '板块图标',
  `meta_key` varchar(255) DEFAULT NULL COMMENT 'Meta关键字',
  `meta_title` varchar(255) DEFAULT NULL COMMENT 'Meta标题',
  `meta_desc` varchar(255) DEFAULT NULL COMMENT 'Meta描述',
  `is_upvote` tinyint(4) DEFAULT NULL COMMENT '是否支持点赞，0否，1是',
  `is_audit` tinyint(4) DEFAULT NULL COMMENT '是否需要审核，0否，1是',
  `is_comment` tinyint(4) DEFAULT NULL COMMENT '是否支持评论，0否，1是',
  `is_recommend` tinyint(4) DEFAULT NULL COMMENT '是否推荐，0否，1是',
  `cids` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `parent_id` (`pid`) USING BTREE,
  KEY `name` (`display_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_category_user-y
-- ----------------------------
DROP TABLE IF EXISTS `ncms_category_user-y`;
CREATE TABLE `ncms_category_user-y` (
  `id` bigint(20) unsigned NOT NULL COMMENT '用户拥有的栏目',
  `user_id` bigint(20) DEFAULT NULL COMMENT '后台用户',
  `category_id` bigint(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_cu_permission-y
-- ----------------------------
DROP TABLE IF EXISTS `ncms_cu_permission-y`;
CREATE TABLE `ncms_cu_permission-y` (
  `id` bigint(20) NOT NULL COMMENT '用户的分类的权限记录表',
  `cu_id` bigint(20) DEFAULT NULL COMMENT 'ncms_category_user的编号',
  `code` varchar(100) DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_datasource
-- ----------------------------
DROP TABLE IF EXISTS `ncms_datasource`;
CREATE TABLE `ncms_datasource` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `driver_class_name` varchar(255) NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT '10000' COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT '10000' COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT '100' COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT '100' COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT '18800' COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT '300000' COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否开启',
  `code` varchar(32) DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) DEFAULT NULL COMMENT '展示名',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_ext_data
-- ----------------------------
DROP TABLE IF EXISTS `ncms_ext_data`;
CREATE TABLE `ncms_ext_data` (
  `id` bigint(20) NOT NULL COMMENT '接收爬虫收集的文章',
  `article_id` varchar(20) DEFAULT NULL COMMENT '文章编号',
  `author_id` varchar(20) DEFAULT NULL COMMENT '作者编号',
  `mask_id` varchar(20) DEFAULT NULL COMMENT 'varchar',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `author` varchar(32) DEFAULT NULL COMMENT '作者',
  `digest` text COMMENT '概要',
  `title` varchar(64) DEFAULT NULL COMMENT '标题',
  `url` varchar(64) DEFAULT NULL COMMENT 'URL',
  `readed` tinyint(4) DEFAULT NULL COMMENT '是否阅读',
  `cover_img_url` text COMMENT '封面图',
  `content` longtext COMMENT '内容',
  `attachments` text COMMENT '附件数组(废弃字段)',
  `memo` varchar(128) DEFAULT NULL COMMENT '预留字段',
  `syn_id` bigint(4) DEFAULT NULL COMMENT 'ncms_articles判断是否同步',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `tnt_art_uk` (`article_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_ext_type
-- ----------------------------
DROP TABLE IF EXISTS `ncms_ext_type`;
CREATE TABLE `ncms_ext_type` (
  `id` bigint(20) NOT NULL COMMENT '爬虫项目类型',
  `project` varchar(64) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  `status` tinyint(4) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `name` (`project`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_fusion_check_cfg
-- ----------------------------
DROP TABLE IF EXISTS `ncms_fusion_check_cfg`;
CREATE TABLE `ncms_fusion_check_cfg` (
  `id` bigint(20) NOT NULL,
  `need_check` tinyint(1) DEFAULT NULL COMMENT '是否需要审核，0否，1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- ----------------------------
-- Table structure for ncms_interaction
-- ----------------------------
DROP TABLE IF EXISTS `ncms_interaction`;
CREATE TABLE `ncms_interaction` (
  `id` bigint(20) unsigned NOT NULL COMMENT '关注／收藏 / 点赞',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `itype` enum('COMMENT','UPVOTE','CLICK','EVALUATE','FAVOR','READ','SHARE') DEFAULT NULL COMMENT '''COMMENT'',''UPVOTE'',''CLICK'',''EVALUATE'',''FAVOR'',''READ'',''SHARE''',
  `pid` bigint(20) DEFAULT NULL COMMENT '父id',
  `article_id` bigint(20) DEFAULT NULL COMMENT '文章id',
  `content` varchar(1024) DEFAULT NULL COMMENT '评论内容',
  `reply` varchar(1024) DEFAULT NULL COMMENT '回复',
  `status` tinyint(4) DEFAULT NULL COMMENT '审核状态,0否，1是',
  `score` int(11) DEFAULT NULL COMMENT '评分',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '位置id',
  `memo` varchar(255) DEFAULT NULL,
  `created_ip` varchar(255) DEFAULT NULL COMMENT '创建ip地址',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间，关注时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间，通知时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间，取消关注',
  `article_type` tinyint(255) DEFAULT NULL COMMENT '阅读/点赞/收藏/评论过的文章类型；1表示文章，2表示视频，3表示活动',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_operate_success_log
-- ----------------------------
DROP TABLE IF EXISTS `ncms_operate_success_log`;
CREATE TABLE `ncms_operate_success_log` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志类型',
  `sub_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志子类型',
  `user_id` bigint(20) DEFAULT NULL COMMENT '系统用户id',
  `action` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志内容',
  `biz_no` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务标识(被操作对象的业务编号)',
  `extra` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '额外',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `type_subtype_aid` (`type`,`sub_type`,`user_id`,`biz_no`) USING BTREE COMMENT '在什么类型下的子类型谁的操作',
  KEY `biz_no` (`type`,`sub_type`,`biz_no`) USING BTREE COMMENT '某个被操作的对象'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS操作成功日志';

-- ----------------------------
-- Table structure for ncms_specialcolumn
-- ----------------------------
DROP TABLE IF EXISTS `ncms_specialcolumn`;
CREATE TABLE `ncms_specialcolumn` (
  `id` bigint(20) unsigned NOT NULL COMMENT '专题',
  `pid` bigint(20) DEFAULT '0' COMMENT '父级版块，默认0',
  `display_name` varchar(64) DEFAULT NULL COMMENT '专题名称',
  `name` varchar(64) DEFAULT NULL COMMENT '版块的英文名，全表唯一，在前台的URL中进行拼接',
  `logo` varchar(256) DEFAULT NULL COMMENT 'logo',
  `description` varchar(255) DEFAULT NULL COMMENT '专题描述',
  `active` tinyint(4) DEFAULT '0' COMMENT '激活状态，默认激活，0否，1是',
  `sort` int(11) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `icon` varchar(255) DEFAULT '板块图标',
  `is_recommend` tinyint(4) DEFAULT '0' COMMENT '是否推荐，0否，1是',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `parent_id` (`pid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_tags
-- ----------------------------
DROP TABLE IF EXISTS `ncms_tags`;
CREATE TABLE `ncms_tags` (
  `id` bigint(20) unsigned NOT NULL COMMENT '标签定义分类',
  `name` varchar(64) DEFAULT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '图标',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `active` tinyint(4) DEFAULT '1' COMMENT '激活状态， 默认激活，0否，是',
  `count` int(11) DEFAULT NULL COMMENT '保留',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_user_account
-- ----------------------------
DROP TABLE IF EXISTS `ncms_user_account`;
CREATE TABLE `ncms_user_account` (
  `id` bigint(20) NOT NULL COMMENT '融媒体账号',
  `author_id` varchar(32) DEFAULT NULL COMMENT '作者编号',
  `status` tinyint(4) DEFAULT '1' COMMENT '1，启用／0，停用',
  `author` varchar(64) DEFAULT NULL COMMENT '作者',
  `account_name` varchar(32) DEFAULT NULL COMMENT '账号',
  `atype_id` bigint(20) DEFAULT NULL COMMENT '账号类型',
  `url` varchar(128) DEFAULT NULL COMMENT '与用户绑定的首页链接',
  `cat_id` bigint(20) DEFAULT NULL COMMENT '同步栏目',
  `cat_ids` varchar(255) DEFAULT NULL,
  `source_id` bigint(20) DEFAULT NULL COMMENT '来源编号',
  `url_type` tinyint(4) DEFAULT NULL COMMENT '使用原文URL，0否，1是',
  `auto_syn` tinyint(4) DEFAULT NULL COMMENT '是否自动同步到图文，0否，1是',
  `memo` varchar(256) DEFAULT NULL,
  `art_url` varchar(128) DEFAULT NULL,
  `count` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL COMMENT '增加的时间',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_user_category_link
-- ----------------------------
DROP TABLE IF EXISTS `ncms_user_category_link`;
CREATE TABLE `ncms_user_category_link` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime DEFAULT NULL COMMENT '关联时间',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `category_ids` varchar(255) DEFAULT NULL COMMENT '关联的分类id集合（包含每一个node）',
  `category_names` varchar(255) DEFAULT NULL COMMENT '关联的分类名',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与文章分类关联表';

-- ----------------------------
-- Table structure for ncms_user_counter
-- ----------------------------
DROP TABLE IF EXISTS `ncms_user_counter`;
CREATE TABLE `ncms_user_counter` (
  `id` bigint(11) unsigned NOT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '前端用户',
  `refer` varchar(255) DEFAULT NULL COMMENT '来源',
  `ref_id` bigint(20) DEFAULT NULL COMMENT '来源表的id',
  `counter` varchar(255) DEFAULT NULL COMMENT '类型',
  `count` int(255) DEFAULT NULL COMMENT '点击数',
  `update_at` datetime DEFAULT NULL COMMENT '最后操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_user_tags
-- ----------------------------
DROP TABLE IF EXISTS `ncms_user_tags`;
CREATE TABLE `ncms_user_tags` (
  `id` bigint(20) NOT NULL,
  `uacc_id` bigint(20) DEFAULT NULL COMMENT 'user_account  表id',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '标签id',
  `created_at` datetime DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_word_hot
-- ----------------------------
DROP TABLE IF EXISTS `ncms_word_hot`;
CREATE TABLE `ncms_word_hot` (
  `id` bigint(20) NOT NULL,
  `name` varchar(64) DEFAULT NULL COMMENT '热词名称',
  `search_times` int(10) unsigned zerofill DEFAULT NULL COMMENT '搜索次数',
  `order_at` int(10) DEFAULT NULL COMMENT '排序',
  `active` tinyint(4) unsigned zerofill DEFAULT '0001' COMMENT '是否推荐，0否，1是',
  `count` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_word_sensitive
-- ----------------------------
DROP TABLE IF EXISTS `ncms_word_sensitive`;
CREATE TABLE `ncms_word_sensitive` (
  `id` bigint(20) NOT NULL,
  `badword` varchar(256) NOT NULL COMMENT '敏感词',
  `active` tinyint(4) DEFAULT NULL COMMENT '是否启用，0否，1是',
  `replacement` varchar(255) DEFAULT NULL COMMENT '替换',
  `rank` tinyint(4) DEFAULT NULL COMMENT '层级',
  `source` enum('TENANT','PLATFORM') DEFAULT NULL COMMENT '来源',
  `platform_id` varchar(20) DEFAULT NULL COMMENT '平台端被同步敏感词id',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `created` (`created_at`) USING BTREE,
  KEY `id` (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for ncms_word_source
-- ----------------------------
DROP TABLE IF EXISTS `ncms_word_source`;
CREATE TABLE `ncms_word_source` (
  `id` bigint(20) NOT NULL,
  `name` varchar(64) NOT NULL COMMENT '来源名称',
  `state` varchar(255) DEFAULT NULL COMMENT '来源说明',
  `active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否有效，0否，1是',
  `count` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for open_ai_camera
-- ----------------------------
DROP TABLE IF EXISTS `open_ai_camera`;
CREATE TABLE `open_ai_camera` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `brand` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备品牌',
  `model` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备型号',
  `serial_no` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '设备序列号',
  `mac` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'MAC地址',
  `region_pid` bigint(20) DEFAULT '0' COMMENT '区id',
  `region_id` bigint(20) DEFAULT '0' COMMENT '街道id',
  `region_cid` bigint(20) DEFAULT '0' COMMENT '社区id',
  `place_id` bigint(20) DEFAULT '0' COMMENT '场所id',
  `point_id` bigint(20) DEFAULT NULL COMMENT '垃圾投放点位id，关联到com_place_delivery_point',
  `cannel` varchar(31) COLLATE utf8mb4_unicode_ci DEFAULT '1' COMMENT '通道号',
  `status` tinyint(4) DEFAULT '0' COMMENT '1-在线 0-离线',
  `type` tinyint(4) DEFAULT NULL COMMENT '接入类型 1-新增 2-环卫云 3-锐明云',
  `is_sound` tinyint(4) DEFAULT NULL COMMENT '是否语音播报 0-否 1-是',
  `memo` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `pics` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片',
  `ip` varchar(24) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'ip地址',
  `gis_id` bigint(20) DEFAULT NULL COMMENT '地图点位id',
  `longitude` decimal(18,15) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(18,15) DEFAULT NULL COMMENT '纬度',
  `address` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地址',
  `flv_address` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'flv 协议播放地址(流畅)',
  `hd_flv_address` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'flv 协议直播地址(高清)',
  `open_ai_camera_platform_id` bigint(20) DEFAULT '1' COMMENT '第三方厂商id 关联 open_ai_camera_platform_info 表',
  `accessed_at` datetime DEFAULT NULL COMMENT '接入时间',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `updated_by` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '修改人',
  `updated_user_id` bigint(20) DEFAULT NULL COMMENT '修改用户ID',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `is_lock` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否锁定 0-正常 1-锁定',
  `last_connect_at` datetime DEFAULT NULL COMMENT '最后在线时间',
  `ext_id` bigint(20) DEFAULT NULL COMMENT '关联id',
  `relevance_type` tinyint(4) DEFAULT NULL COMMENT '绑定场所类型 1-投放点 2-基地',
  `online_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `device_id` bigint(20) DEFAULT NULL COMMENT 'device_id',
  `lg_datav_sort` int(11) DEFAULT NULL COMMENT '仅用于龙岗大屏排序',
  `monitor_name` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '通道名称',
  `channel_id` bigint(20) DEFAULT '0' COMMENT '通道id',
  `original_serial_no` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原始序列号',
  `monitor_no` varchar(127) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '监控设备号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_point_id` (`point_id`) USING BTREE,
  KEY `idx_cannel_isdeleted` (`cannel`,`is_deleted`) USING BTREE,
  KEY `idx_region` (`region_pid`,`region_id`,`region_cid`,`place_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='开放 ai 摄像头表';

-- ----------------------------
-- Table structure for pubshare_approval_log
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_approval_log`;
CREATE TABLE `pubshare_approval_log` (
  `id` bigint(20) NOT NULL COMMENT '稿件审核',
  `sid` bigint(20) DEFAULT NULL COMMENT '资源id',
  `operation` tinyint(4) DEFAULT NULL COMMENT '审核状态，0待审核，1通过，2拒绝',
  `suggestion` varchar(1024) DEFAULT NULL COMMENT '审核意见',
  `user_id` bigint(20) DEFAULT NULL COMMENT '审批用户id',
  `created_at` datetime DEFAULT NULL COMMENT '审批时间',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(255) DEFAULT NULL COMMENT '备注\r\n',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_config
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_config`;
CREATE TABLE `pubshare_config` (
  `id` int(10) unsigned NOT NULL,
  `cnf_key` varchar(256) DEFAULT NULL COMMENT 'key保存配置名，\r\nvalue保存配置值。pubshare_config投稿配置（0所有单位1选定单位3不对外投稿）',
  `cnf_value` varchar(256) DEFAULT NULL COMMENT '配置的值。可以定为0-1-2',
  `deleted_at` datetime DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_config_list
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_config_list`;
CREATE TABLE `pubshare_config_list` (
  `id` bigint(20) NOT NULL,
  `op_type` tinyint(4) DEFAULT NULL COMMENT '操作标识，0表示发送，1表示接受',
  `op_tenant_id` bigint(20) DEFAULT NULL COMMENT '白名单或者黑名单的租户ID',
  `created_at` datetime DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL COMMENT '当前商户id',
  `config_id` bigint(20) DEFAULT NULL COMMENT 'pubshare_config表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_datasource
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_datasource`;
CREATE TABLE `pubshare_datasource` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `driver_class_name` varchar(255) NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT '10000' COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT '10000' COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT '100' COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT '100' COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT '18800' COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT '300000' COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否开启',
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `code` varchar(32) DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) DEFAULT NULL COMMENT '展示名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_interaction
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_interaction`;
CREATE TABLE `pubshare_interaction` (
  `id` bigint(20) unsigned NOT NULL COMMENT '关注／收藏 / 点赞',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `itype` enum('COMMENT','UPVOTE','CLICK','EVALUATE','FAVOR','READ') DEFAULT NULL COMMENT '''COMMENT'',''UPVOTE'',''CLICK'',''EVALUATE'',''FAVOR'',''READ''',
  `pid` bigint(20) DEFAULT NULL COMMENT '父id',
  `article_id` bigint(20) DEFAULT NULL COMMENT '文章id',
  `content` varchar(1024) DEFAULT NULL COMMENT '评论内容',
  `reply` varchar(1024) DEFAULT NULL COMMENT '回复',
  `status` tinyint(4) DEFAULT NULL COMMENT '审核状态,0否，1是',
  `score` int(11) DEFAULT NULL COMMENT '评分',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '位置id',
  `memo` varchar(255) DEFAULT NULL,
  `created_ip` varchar(255) DEFAULT NULL COMMENT '创建ip地址',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间，关注时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间，通知时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间，取消关注',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `article_id` (`article_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_m_articles
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_m_articles`;
CREATE TABLE `pubshare_m_articles` (
  `id` bigint(20) unsigned NOT NULL COMMENT 'cms接收',
  `type` tinyint(255) DEFAULT NULL COMMENT '文章类型,0内容图文,1链接图文,2坐标图文',
  `source` varchar(256) DEFAULT NULL COMMENT '来源',
  `author` varchar(128) DEFAULT NULL COMMENT '作者',
  `title` varchar(1024) DEFAULT NULL COMMENT '文章标题',
  `digest` varchar(1024) DEFAULT NULL COMMENT '文章摘要',
  `active` tinyint(4) DEFAULT '1' COMMENT '状态   0不显示  1显示',
  `picture_id` varchar(256) DEFAULT NULL COMMENT '封面resource_upload的id',
  `content` longtext COMMENT '内容',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_ip` varchar(128) DEFAULT NULL COMMENT '创建IP',
  `updated_at` datetime DEFAULT NULL COMMENT '最近更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `refer_url` varchar(1024) DEFAULT NULL COMMENT '关联链接',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `share_id` varchar(20) DEFAULT NULL COMMENT 'sharelist表的id',
  `address` varchar(256) DEFAULT NULL COMMENT '地址',
  `status` tinyint(4) DEFAULT '0' COMMENT '审核状态0.待审核 1.已审核 2.退回 3.已归档 4.出档 5.审核中 6. 已发布',
  `memo` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_m_resource
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_m_resource`;
CREATE TABLE `pubshare_m_resource` (
  `id` bigint(20) NOT NULL COMMENT '共享资源表',
  `type` enum('PICTURE','VIDEO','AUDIO','FILE','TEMPLATE','STYLE','PERSONALTEMPLATE') DEFAULT NULL COMMENT '''PICTURE'',''VIDEO'',''MUSIC'',''FILE''',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `author` varchar(255) DEFAULT NULL COMMENT '作者',
  `user_id` bigint(20) DEFAULT NULL,
  `title` varchar(255) DEFAULT NULL COMMENT '资源名称',
  `active` tinyint(4) DEFAULT NULL COMMENT '是否显示，0否，1是',
  `content` longtext COMMENT '内容',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `share_id` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(255) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_m_wx_media
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_m_wx_media`;
CREATE TABLE `pubshare_m_wx_media` (
  `id` bigint(11) unsigned NOT NULL COMMENT '所有内容都以此表建立系统内media_id, 图文, 多图文, 图片, 视频, 音乐, 语音',
  `acct_id` bigint(11) DEFAULT NULL COMMENT '公众号id',
  `type` varchar(16) DEFAULT NULL COMMENT '微信媒体类型',
  `name` varchar(1024) DEFAULT NULL,
  `media_id` varchar(1024) DEFAULT NULL COMMENT 'media_id',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `is_expired` tinyint(4) DEFAULT NULL COMMENT '是否过期',
  `wx_url` varchar(1024) DEFAULT NULL COMMENT '微信返回的url',
  `wx_created_at` timestamp NULL DEFAULT NULL COMMENT '微信返回的创建时间',
  `title` varchar(128) DEFAULT NULL COMMENT '标题',
  `description` text COMMENT '描述',
  `expired_type` varchar(16) DEFAULT NULL COMMENT '素材类型：perm永久，temp临时',
  `share_id` varchar(20) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` longtext,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `type_2` (`type`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_m_wx_news
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_m_wx_news`;
CREATE TABLE `pubshare_m_wx_news` (
  `id` bigint(11) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL,
  `ref_id` bigint(11) DEFAULT NULL COMMENT 'id of wx_media',
  `ref_idx` bigint(11) DEFAULT NULL,
  `type` varchar(16) DEFAULT NULL,
  `media_id` varchar(128) DEFAULT NULL,
  `title` varchar(256) DEFAULT NULL,
  `thumb_media_id` varchar(1024) DEFAULT NULL,
  `thumb_resource_id` bigint(20) DEFAULT NULL,
  `show_cover_pic` tinyint(4) DEFAULT NULL,
  `author` varchar(128) DEFAULT NULL,
  `digest` varchar(1024) DEFAULT NULL,
  `url` varchar(1024) DEFAULT NULL,
  `content_source_url` varchar(1024) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  `content` longtext,
  `description` varchar(1024) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `created_at` datetime DEFAULT NULL,
  `picture` varchar(1024) DEFAULT NULL,
  `share_id` varchar(20) DEFAULT NULL,
  `created_ip` varchar(128) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) DEFAULT NULL,
  `recommend` varchar(1024) DEFAULT NULL COMMENT '推荐语',
  `is_original` tinyint(4) DEFAULT NULL COMMENT '是否原创',
  `is_share` tinyint(4) DEFAULT NULL COMMENT '是否共享',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`,`ref_id`,`ref_idx`) USING BTREE,
  KEY `ref_id` (`ref_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for pubshare_sharelist
-- ----------------------------
DROP TABLE IF EXISTS `pubshare_sharelist`;
CREATE TABLE `pubshare_sharelist` (
  `id` bigint(20) NOT NULL COMMENT '稿件审核',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `related_msgid` varchar(255) DEFAULT NULL COMMENT '关联的微信稿件的msgid',
  `type` enum('WXNEWS','WXMEDIAL','CMS','RESOURCE') DEFAULT NULL COMMENT '''WXNEWS'',''WXMEDIAL'',''CMS'',''RESOURCE''',
  `material_type` enum('IMAGE','VIDEO','VOICE','FILE','TEXT') CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci DEFAULT NULL COMMENT '''IMAGE'',''VIDEO'',''VOICE'',''FILE'',''TEXT''',
  `title` varchar(512) DEFAULT NULL COMMENT '稿件名称',
  `is_push` tinyint(4) DEFAULT NULL COMMENT '收发类型，0收，1发',
  `tenant_name` varchar(255) DEFAULT NULL COMMENT '租户名称',
  `scid` varchar(64) DEFAULT NULL COMMENT '租户id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '投稿人id',
  `user_name` varchar(255) DEFAULT NULL COMMENT '投稿人',
  `phone` varchar(255) DEFAULT NULL COMMENT '联系电话',
  `description` varchar(1024) DEFAULT NULL COMMENT '内容',
  `score` double(20,0) DEFAULT NULL COMMENT '评分',
  `status` tinyint(4) DEFAULT NULL COMMENT '审核状态，0待审核，1通过，2拒绝',
  `approval_suggestion` varchar(1024) DEFAULT NULL COMMENT '审核意见',
  `approval_at` datetime DEFAULT NULL COMMENT '审批时间',
  `approval_by` bigint(20) DEFAULT NULL COMMENT '审批用户id',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(255) DEFAULT NULL COMMENT '备注\r\n',
  `read_count` int(11) DEFAULT '0' COMMENT '关联微信稿件的阅读量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_category
-- ----------------------------
DROP TABLE IF EXISTS `resource_category`;
CREATE TABLE `resource_category` (
  `id` bigint(25) unsigned NOT NULL COMMENT 'id',
  `pid` bigint(20) DEFAULT '0' COMMENT '父级分类，默认0',
  `display_name` varchar(64) DEFAULT NULL COMMENT '分类名称，全表唯一，在前台的URL中进行拼接',
  `name` varchar(64) DEFAULT NULL COMMENT '分类的英文名',
  `tab` enum('PICTURE','VIDEO','AUDIO','FILE','TEMPLATE','STYLE','PERSONALTEMPLATE','PERSONALPICTURE') DEFAULT NULL COMMENT '''PICTURE'',''VIDEO'',''AUDIO'',''FILE'',''TEMPLATE'',''STYLE'',''PERSONALTEMPLATE'',''PERSONALPICTURE''',
  `type` enum('PLATFORM','TENANT') DEFAULT NULL COMMENT '平台端platform，商户端tenant',
  `logo` varchar(256) DEFAULT NULL COMMENT 'logo',
  `description` varchar(255) DEFAULT NULL COMMENT '分类描述',
  `user_id` bigint(25) DEFAULT NULL COMMENT '用户id',
  `tenant_id` bigint(25) DEFAULT NULL COMMENT '商户id',
  `tier` tinyint(255) DEFAULT NULL COMMENT '当前分类的层级',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `status` tinyint(4) DEFAULT '0' COMMENT '激活状态，默认激活，可见',
  `sort` int(11) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `icon` varchar(255) DEFAULT '分类图标',
  `meta_key` varchar(255) DEFAULT NULL,
  `meta_title` varchar(255) DEFAULT NULL,
  `meta_desc` varchar(255) DEFAULT NULL,
  `is_upvote` tinyint(4) DEFAULT NULL COMMENT '是否支持点赞',
  `is_audit` tinyint(4) DEFAULT NULL COMMENT '是否需要审核',
  `is_comment` tinyint(4) DEFAULT NULL COMMENT '是否支持评论',
  `is_recommend` tinyint(4) DEFAULT NULL,
  `sync_id` bigint(25) DEFAULT NULL COMMENT '租户端同步平台端的资源项id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `parent_id` (`pid`) USING BTREE,
  KEY `displayname` (`display_name`) USING BTREE,
  KEY `sync_id` (`sync_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_datasource
-- ----------------------------
DROP TABLE IF EXISTS `resource_datasource`;
CREATE TABLE `resource_datasource` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `driver_class_name` varchar(255) NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT '10000' COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT '10000' COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT '100' COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT '100' COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT '18800' COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT '300000' COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否开启',
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `code` varchar(32) DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) DEFAULT NULL COMMENT '展示名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_item
-- ----------------------------
DROP TABLE IF EXISTS `resource_item`;
CREATE TABLE `resource_item` (
  `id` bigint(20) unsigned NOT NULL COMMENT 'cms接收',
  `type` enum('PICTURE','VIDEO','AUDIO','FILE','TEMPLATE','STYLE','PERSONALTEMPLATE') DEFAULT NULL COMMENT '''PICTUER'',''VIDEO'',''AUDIO'',''FILE'',''TEMPLATE'',''STYLE'',''PERSONALTEMPLATE''（个人模板）',
  `category_id` bigint(20) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '发布者',
  `source` varchar(256) DEFAULT NULL COMMENT '来源',
  `author` varchar(128) DEFAULT NULL COMMENT '作者',
  `title` varchar(1024) DEFAULT NULL COMMENT '标题',
  `active` tinyint(4) DEFAULT '1' COMMENT '状态   0不显示  1显示',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `created_ip` varchar(128) DEFAULT NULL COMMENT '创建IP',
  `updated_at` datetime DEFAULT NULL COMMENT '最近更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `refer_url` varchar(1024) DEFAULT NULL COMMENT '关联链接',
  `is_prime` tinyint(4) DEFAULT '0' COMMENT '精华帖,1是,0不是,',
  `anonymous` tinyint(4) DEFAULT '0' COMMENT '是否匿名 1是 0不是',
  `content` longtext COMMENT '文章内容',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `address` varchar(256) DEFAULT NULL COMMENT '地址',
  `status` tinyint(4) DEFAULT '0' COMMENT '审核状态0.待审核 1.已审核 2.退回 3.已归档 4.出档 5.审核中 6. 已发布',
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `digest` varchar(1024) DEFAULT NULL COMMENT '摘要',
  `is_trend` tinyint(4) DEFAULT '0' COMMENT '热点 1:是 0否',
  `is_push` tinyint(4) DEFAULT NULL COMMENT '必推，0否，1是',
  `is_topped` tinyint(4) DEFAULT '0' COMMENT '置顶，1：置顶，0：非置顶',
  `is_original` tinyint(4) DEFAULT '0' COMMENT '是否原创',
  `is_recommend` int(4) DEFAULT NULL COMMENT '推荐，默认0',
  `is_share` tinyint(4) DEFAULT '0' COMMENT '是否允许共享，1允许，0不允许',
  `memo` varchar(255) DEFAULT NULL,
  `cids` varchar(255) DEFAULT NULL,
  `is_favor` tinyint(255) DEFAULT NULL COMMENT '是否收藏，0否，1是',
  `source_id` bigint(20) DEFAULT NULL COMMENT '上传资源id',
  `route` varchar(255) DEFAULT NULL COMMENT '分类的父级id',
  `sid` varchar(255) DEFAULT NULL COMMENT '爬虫平台提供的唯一ID标示',
  `user_name` varchar(255) DEFAULT NULL COMMENT '用户名',
  `sync_id` bigint(25) DEFAULT NULL COMMENT '租户端同步平台端的资源项id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `inx_user_id` (`id`,`user_id`) USING BTREE,
  KEY `source_id` (`source_id`) USING BTREE,
  KEY `sync_id` (`sync_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_item_account
-- ----------------------------
DROP TABLE IF EXISTS `resource_item_account`;
CREATE TABLE `resource_item_account` (
  `id` bigint(20) NOT NULL,
  `item_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `account_id` bigint(20) DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `item_id` (`item_id`) USING BTREE,
  KEY `account_id` (`account_id`) USING BTREE,
  KEY `item_account` (`item_id`,`account_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_item_tag
-- ----------------------------
DROP TABLE IF EXISTS `resource_item_tag`;
CREATE TABLE `resource_item_tag` (
  `id` bigint(20) NOT NULL,
  `item_id` bigint(20) DEFAULT NULL COMMENT ' 资源id',
  `tag_id` bigint(20) DEFAULT NULL COMMENT '标签id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `item_id` (`item_id`) USING BTREE,
  KEY `tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_item_user
-- ----------------------------
DROP TABLE IF EXISTS `resource_item_user`;
CREATE TABLE `resource_item_user` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `item_id` bigint(20) DEFAULT NULL COMMENT '资源项id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `category_id` bigint(20) DEFAULT NULL COMMENT '修改后的分类id',
  `is_favor` tinyint(4) DEFAULT NULL COMMENT '是否收藏  0否  1是',
  `is_topped` tinyint(4) DEFAULT NULL COMMENT '是否置顶，0否  1是',
  `update_title` varchar(255) DEFAULT NULL COMMENT '资源项标题',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间(置顶)',
  `favor_updated_at` datetime DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `inx_item_user_favor` (`item_id`,`user_id`,`is_favor`) USING BTREE,
  KEY `item_id` (`item_id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_item_user_count
-- ----------------------------
DROP TABLE IF EXISTS `resource_item_user_count`;
CREATE TABLE `resource_item_user_count` (
  `id` bigint(20) NOT NULL,
  `counter_id` bigint(20) DEFAULT NULL COMMENT '统计数据的id',
  `item_id` bigint(20) DEFAULT NULL COMMENT '资源项id',
  `tenant_id` bigint(20) DEFAULT NULL COMMENT '商户id',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `title` varchar(255) DEFAULT NULL COMMENT '名称',
  `version` bigint(20) DEFAULT '0' COMMENT '搜索次数',
  `memo` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for resource_tag
-- ----------------------------
DROP TABLE IF EXISTS `resource_tag`;
CREATE TABLE `resource_tag` (
  `id` bigint(20) NOT NULL,
  `title` varchar(255) DEFAULT NULL COMMENT '标签名称',
  `memo` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sms_code_log
-- ----------------------------
DROP TABLE IF EXISTS `sms_code_log`;
CREATE TABLE `sms_code_log` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `mobile` varchar(16) DEFAULT NULL COMMENT '手机号码',
  `code` varchar(16) DEFAULT NULL COMMENT '验证码',
  `content` varchar(1024) DEFAULT NULL COMMENT '发送的内容',
  `resp` varchar(1024) DEFAULT NULL COMMENT '返回的内容',
  `source` varchar(128) DEFAULT NULL COMMENT '发送源',
  `create_on` varchar(128) DEFAULT NULL COMMENT '创建ip',
  `remark` varchar(1024) DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `template_id` bigint(20) DEFAULT NULL COMMENT '模板编号',
  `account_id` bigint(20) DEFAULT NULL COMMENT '账号编号',
  `status` tinyint(4) DEFAULT '0' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sms_msg_account
-- ----------------------------
DROP TABLE IF EXISTS `sms_msg_account`;
CREATE TABLE `sms_msg_account` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '短信账号表',
  `account` varchar(200) DEFAULT NULL COMMENT '账号',
  `password` varchar(200) DEFAULT NULL COMMENT '密码',
  `type` tinyint(4) DEFAULT NULL COMMENT '类型 1:云之讯 2.阿里云 3.无线',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `param_json` varchar(1000) DEFAULT NULL COMMENT '其余参数',
  `is_active` tinyint(4) DEFAULT '0' COMMENT '是否开启',
  `url` varchar(256) DEFAULT NULL COMMENT '请求链接',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  `sort` int(12) DEFAULT NULL COMMENT '顺序',
  `send_uri` varchar(256) DEFAULT NULL COMMENT '发送接口',
  `batch_send_uri` varchar(256) DEFAULT NULL COMMENT '批量发送接口',
  `add_template_uri` varchar(256) DEFAULT NULL COMMENT '增加模板接口',
  `del_template_uri` varchar(256) DEFAULT NULL COMMENT '删除模板接口',
  `update_template_uri` varchar(256) DEFAULT NULL COMMENT '修改模板接口',
  `get_template_uri` varchar(256) DEFAULT NULL COMMENT '获取模板接口',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sms_msg_template
-- ----------------------------
DROP TABLE IF EXISTS `sms_msg_template`;
CREATE TABLE `sms_msg_template` (
  `id` bigint(20) NOT NULL COMMENT '短信模板',
  `name` varchar(255) DEFAULT NULL COMMENT '模板名',
  `type` tinyint(4) DEFAULT NULL COMMENT '模板类型 1:云之讯 2.阿里云 3.无线',
  `content` text COMMENT '内容',
  `url` varchar(255) DEFAULT NULL COMMENT '请求链接',
  `template_id` varchar(11) DEFAULT NULL COMMENT '模板编号',
  `state` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否可用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `refer` varchar(255) DEFAULT NULL COMMENT '关联',
  `account_id` bigint(20) DEFAULT NULL COMMENT 'sms_msg_account的编号',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `template_id` (`template_id`) USING BTREE,
  KEY `refer` (`refer`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sporadic_project
-- ----------------------------
DROP TABLE IF EXISTS `sporadic_project`;
CREATE TABLE `sporadic_project` (
  `id` bigint(20) NOT NULL COMMENT '工程ID',
  `name` varchar(64) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '工程名称',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id，关联xsgc_organization',
  `cate_pid` bigint(20) DEFAULT NULL COMMENT '工程分类ID',
  `cate_id` bigint(20) DEFAULT NULL COMMENT '工程类别ID',
  `amount` decimal(12,2) DEFAULT NULL COMMENT '工程金额(元)',
  `area` decimal(8,2) DEFAULT NULL COMMENT '实际施工面积',
  `start_at` date DEFAULT NULL COMMENT '工程开始时间',
  `end_at` date DEFAULT NULL COMMENT '工程结束时间',
  `region_pid` bigint(20) DEFAULT NULL COMMENT '所在区',
  `region_id` bigint(20) DEFAULT NULL COMMENT '所在街道',
  `region_cid` bigint(20) DEFAULT NULL COMMENT '所在社区',
  `address` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详细地址',
  `lng` decimal(13,10) DEFAULT NULL COMMENT '经度',
  `lat` decimal(13,10) DEFAULT NULL COMMENT '纬度',
  `constructor_id` bigint(20) DEFAULT NULL COMMENT '建设单位ID',
  `constructor_name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建设单位名称',
  `constructor_charger_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建设单位负责人ID',
  `constructor_charger` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '建设单位负责人',
  `owner_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业主电话',
  `contractor_id` bigint(20) DEFAULT NULL COMMENT '施工单位ID',
  `contractor_name` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位名称',
  `contractor_charger_id` bigint(20) DEFAULT NULL COMMENT '施工单位负责人ID',
  `contractor_charger` varchar(32) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人',
  `contractor_charger_mobile` varchar(16) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '施工单位负责人电话',
  `project_number` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备案编号',
  `status` tinyint(4) DEFAULT '0' COMMENT '施工状态，0-未开始施工，1-施工中，2-施工已结束',
  `create_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `monitor_flag` tinyint(4) DEFAULT '0' COMMENT '是否已接入监管，0-否，1-是',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '点位id，对应gis_poi 表的id',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程表';

-- ----------------------------
-- Table structure for sporadic_project_category
-- ----------------------------
DROP TABLE IF EXISTS `sporadic_project_category`;
CREATE TABLE `sporadic_project_category` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类名称',
  `p_id` bigint(20) DEFAULT NULL COMMENT '父分类ID',
  `active` tinyint(4) DEFAULT '1' COMMENT '状态:0-关闭,1-开启',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1713788653130366977 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程分类';

-- ----------------------------
-- Table structure for sporadic_project_memo
-- ----------------------------
DROP TABLE IF EXISTS `sporadic_project_memo`;
CREATE TABLE `sporadic_project_memo` (
  `id` bigint(20) NOT NULL COMMENT 'ID',
  `project_id` bigint(20) DEFAULT NULL COMMENT '工程ID',
  `content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注内容',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除:0-否,1-是',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-工程备注表';

-- ----------------------------
-- Table structure for sys_app
-- ----------------------------
DROP TABLE IF EXISTS `sys_app`;
CREATE TABLE `sys_app` (
  `id` bigint(20) NOT NULL COMMENT '应用',
  `display_name` varchar(64) DEFAULT NULL COMMENT '应用名称',
  `name` varchar(64) DEFAULT NULL COMMENT '应用标识',
  `icon` varchar(1024) DEFAULT NULL COMMENT '应用图标',
  `biref` varchar(1024) DEFAULT NULL COMMENT '应用简介',
  `status` tinyint(4) DEFAULT NULL COMMENT '有效状态',
  `created_at` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `memo` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_datasource
-- ----------------------------
DROP TABLE IF EXISTS `sys_datasource`;
CREATE TABLE `sys_datasource` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `driver_class_name` varchar(255) NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) NOT NULL,
  `username` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT '10000' COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT '10000' COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT '100' COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT '100' COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT '18800' COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT '300000' COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT '1' COMMENT '是否开启',
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `code` varchar(32) DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) DEFAULT NULL COMMENT '展示名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_ext
-- ----------------------------
DROP TABLE IF EXISTS `sys_ext`;
CREATE TABLE `sys_ext` (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `key_id` bigint(20) DEFAULT NULL COMMENT '键',
  `value` varchar(256) DEFAULT NULL COMMENT '键的值',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `key_id` (`key_id`) USING BTREE,
  KEY `val_id` (`value`(255)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_ext_key
-- ----------------------------
DROP TABLE IF EXISTS `sys_ext_key`;
CREATE TABLE `sys_ext_key` (
  `id` bigint(20) NOT NULL,
  `label` varchar(128) DEFAULT NULL COMMENT '键名',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `label` (`label`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu` (
  `id` bigint(20) NOT NULL,
  `name` varchar(64) NOT NULL COMMENT '菜单标识',
  `display_name` varchar(128) NOT NULL DEFAULT '' COMMENT '菜单展示名称',
  `icon` varchar(128) DEFAULT NULL COMMENT '菜单的icon',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '菜单父id',
  `sort` int(20) NOT NULL DEFAULT '0' COMMENT '排序',
  `route` varchar(128) DEFAULT '',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  `active` tinyint(4) DEFAULT '1' COMMENT '状态(1:正常,0:停用)',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `url` varchar(1024) DEFAULT NULL COMMENT '路由',
  `version` varchar(20) DEFAULT NULL COMMENT '菜单版本',
  `sidebar` tinyint(4) DEFAULT NULL COMMENT '是否现在在菜单列表中',
  `active_icon` varchar(128) DEFAULT NULL COMMENT '激活图标',
  `payload` varchar(1024) DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='菜单表';

-- ----------------------------
-- Table structure for sys_menu_permission
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu_permission`;
CREATE TABLE `sys_menu_permission` (
  `id` bigint(20) NOT NULL,
  `menu_id` bigint(20) NOT NULL COMMENT '菜单id',
  `permission_id` bigint(20) NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `menu_permission_unique` (`menu_id`,`permission_id`) USING BTREE,
  KEY `permission_id_NORMAL` (`permission_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_menu_view_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu_view_log`;
CREATE TABLE `sys_menu_view_log` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `ref_type` tinyint(1) DEFAULT NULL COMMENT '菜单来源。1-移动端；2-管理端',
  `ref_id` bigint(20) DEFAULT NULL COMMENT '来源id',
  `ref_url` varchar(512) DEFAULT NULL COMMENT '来源页面url',
  `bk_name` varchar(64) DEFAULT NULL COMMENT '菜单名',
  `bk_url` varchar(512) DEFAULT NULL COMMENT '菜单跳转路由',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否已删除。0-否；1-是',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单点击记录';

-- ----------------------------
-- Table structure for sys_operate_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_operate_log`;
CREATE TABLE `sys_operate_log` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作员',
  `created_ip` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL COMMENT '操作说明',
  `module` varchar(255) DEFAULT NULL COMMENT '操作模块',
  `agent` varchar(1000) DEFAULT NULL,
  `mark` varchar(1000) DEFAULT NULL COMMENT '标识',
  `url` varchar(1000) DEFAULT NULL COMMENT '请求',
  `param` varchar(1000) DEFAULT NULL COMMENT '参数',
  `result` varchar(1000) DEFAULT NULL COMMENT '结果',
  `user_type` tinyint(4) DEFAULT NULL COMMENT '用户类型 1.管理端 2.微信端',
  `ip` varchar(64) DEFAULT NULL COMMENT 'ip',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_permission_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_permission_role`;
CREATE TABLE `sys_permission_role` (
  `id` bigint(20) NOT NULL,
  `permission_id` bigint(20) unsigned NOT NULL COMMENT '权限id',
  `role_id` bigint(20) unsigned NOT NULL COMMENT '角色id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `permission_role_unique` (`permission_id`,`role_id`) USING BTREE,
  KEY `role_id_foreign` (`role_id`) USING BTREE,
  KEY `permission_id_foreign` (`permission_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='权限与用户组角色对应关系表';

-- ----------------------------
-- Table structure for sys_permissions
-- ----------------------------
DROP TABLE IF EXISTS `sys_permissions`;
CREATE TABLE `sys_permissions` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名',
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限展示名',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '详情',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `active` tinyint(4) DEFAULT '1' COMMENT '是否开启',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='权限信息表';

-- ----------------------------
-- Table structure for sys_role_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_user`;
CREATE TABLE `sys_role_user` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL COMMENT '菜单id',
  `role_id` bigint(20) NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKbxq0ovvpqxj20etjklj85hhs4` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for sys_role_user_organization
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_user_organization`;
CREATE TABLE `sys_role_user_organization` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `role_id` bigint(20) NOT NULL COMMENT '角色id',
  `organization_id` bigint(20) NOT NULL COMMENT '组织id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `FKbxq0ovvpqxj20etjklj85hhs4` (`role_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='系统角色，权限，组织关联表';

-- ----------------------------
-- Table structure for sys_roles
-- ----------------------------
DROP TABLE IF EXISTS `sys_roles`;
CREATE TABLE `sys_roles` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色展示名',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '角色描述',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `type` tinyint(4) DEFAULT NULL COMMENT '角色类型 1.系统 2.商户',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '资源标识',
  `active` tinyint(4) DEFAULT NULL COMMENT '是否开启',
  `flag` tinyint(4) DEFAULT '0' COMMENT '特殊标记，此字段为1时，需要联表sys_role_user_organization查询，有记录时，角色才能生效',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='用户组角色表';

-- ----------------------------
-- Table structure for sys_user_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_config`;
CREATE TABLE `sys_user_config` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `business_key` varchar(100) DEFAULT NULL COMMENT 'key-业务名称',
  `business_value` varchar(256) DEFAULT NULL COMMENT 'value-配置项',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置表';

-- ----------------------------
-- Table structure for sys_users
-- ----------------------------
DROP TABLE IF EXISTS `sys_users`;
CREATE TABLE `sys_users` (
  `id` bigint(20) NOT NULL,
  `username` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户登录名',
  `password` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户密码',
  `nickname` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户屏显昵称，可以不同用户登录名',
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `realname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户真实姓名',
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `pid_card_thumb1` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证证件正面（印有国徽图案、签发机关和有效期限）照片',
  `pid_card_thumb2` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证证件反面（印有个人基本信息和照片）照片',
  `avatar` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户个人图像',
  `phone` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `address` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系地址',
  `emergency_contact` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '紧急联系人信息',
  `servicer_id` int(12) DEFAULT '0' COMMENT '专属客服id，（为0表示其为无专属客服的管理用户）',
  `deleted_at` datetime DEFAULT NULL COMMENT '被软删除时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改更新时间',
  `is_lock` tinyint(3) DEFAULT '0' COMMENT '是否锁定限制用户登录，1锁定,0正常',
  `confirmation_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '确认码',
  `confirmed` tinyint(1) DEFAULT '0' COMMENT '是否已通过验证 0：未通过 1：通过',
  `remember_token` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Laravel 追加的记住令牌',
  `info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `sex` tinyint(4) DEFAULT NULL COMMENT '性别：1，男；2，女；3，保密',
  `job_number` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工号',
  `enterprise_email` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业邮箱',
  `location` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '办公地点',
  `birth` datetime DEFAULT NULL COMMENT '生日',
  `sign` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人签名',
  `region_id` bigint(20) DEFAULT NULL COMMENT '地区',
  `qr` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信二维码',
  `type` tinyint(4) DEFAULT '1' COMMENT '用户类型  0.游客 1.后台 2.微信 ',
  `verified` tinyint(4) DEFAULT NULL COMMENT '实名认证 0.未审核 1.通过 2.未通过 3.取消',
  `verify_time` datetime DEFAULT NULL COMMENT '申请实名认证时间',
  `verified_at` datetime DEFAULT NULL COMMENT '认证时间',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核意见',
  `last_time` datetime DEFAULT NULL COMMENT '最后一次访问时间',
  `last_ip` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一次访问ip',
  `login_times` int(11) DEFAULT NULL COMMENT '登录次数',
  `last_organization` bigint(20) DEFAULT NULL COMMENT '上次选择的组织-管理端，关联xsgc_organization',
  `last_organization_mobile` bigint(20) DEFAULT NULL COMMENT '上次选择的组织-移动端，关联xsgc_organization',
  PRIMARY KEY (`id`),
  KEY `user_nickname_index` (`nickname`(191)),
  KEY `user_phone_index` (`phone`),
  KEY `user_username_idk` (`username`(191)),
  KEY `user_pid_ink` (`pid`),
  KEY `user_email_ink` (`email`),
  KEY `user_realname_index` (`realname`(191)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- ----------------------------
-- Table structure for sys_usr_login_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_usr_login_log`;
CREATE TABLE `sys_usr_login_log` (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
  `username` varchar(256) DEFAULT NULL COMMENT '用户名',
  `phone` varchar(256) DEFAULT NULL COMMENT '手机号码',
  `os` varchar(128) NOT NULL DEFAULT '' COMMENT '操作系统',
  `engine` varchar(20) NOT NULL DEFAULT '' COMMENT '引擎',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '平台来源 wx/ali/sc',
  `way` varchar(255) NOT NULL DEFAULT '' COMMENT '登录方式',
  `client_type` varchar(20) DEFAULT '' COMMENT '客户端类型 app/browser/mp',
  `client` varchar(20) NOT NULL COMMENT '客户端 browser/wx_browser/wx_gov/wx_mp/ilh_app/isz_app/ali_mp',
  `user_agent` varchar(1024) DEFAULT NULL COMMENT 'userAgent',
  `referer` varchar(1024) DEFAULT NULL COMMENT 'referer',
  `event` varchar(20) DEFAULT NULL COMMENT '事件 login logout ',
  `memo` varchar(255) DEFAULT NULL COMMENT '备注',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1.成功 0.失败',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `created_ip` varchar(256) NOT NULL COMMENT '创建ip',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否被删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `uid_idx` (`user_id`,`is_deleted`) USING BTREE,
  KEY `name_idx` (`username`,`is_deleted`) USING BTREE,
  KEY `ct_idx` (`client_type`,`is_deleted`) USING BTREE,
  KEY `w_idx` (`way`,`is_deleted`) USING BTREE,
  KEY `client_idx` (`client`,`is_deleted`) USING BTREE,
  KEY `phone_idx` (`phone`,`is_deleted`) USING BTREE,
  KEY `ip_idx` (`created_ip`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for third_event_push_log
-- ----------------------------
DROP TABLE IF EXISTS `third_event_push_log`;
CREATE TABLE `third_event_push_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `third_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '第三方名称',
  `content` longtext COLLATE utf8mb4_unicode_ci COMMENT '内容',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC COMMENT='第三方事件推送日志';

-- ----------------------------
-- Table structure for uaa_operate_success_log
-- ----------------------------
DROP TABLE IF EXISTS `uaa_operate_success_log`;
CREATE TABLE `uaa_operate_success_log` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志类型',
  `sub_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志子类型',
  `user_id` bigint(20) DEFAULT NULL COMMENT '系统用户id',
  `action` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志内容',
  `biz_no` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务标识(被操作对象的业务编号)',
  `extra` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '额外',
  PRIMARY KEY (`id`),
  KEY `type_subtype_aid` (`type`,`sub_type`,`user_id`,`biz_no`) USING BTREE COMMENT '在什么类型下的子类型谁的操作'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Uaa操作成功日志（原则上该表仅记录成功的业务记录）';

-- ----------------------------
-- Table structure for wx_article
-- ----------------------------
DROP TABLE IF EXISTS `wx_article`;
CREATE TABLE `wx_article` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `appid` varchar(40) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信appid（对应公众号）',
  `title` varchar(512) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '文章标题',
  `mid` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信文章url中的mid',
  `midx` tinyint(3) DEFAULT '1' COMMENT '文章索引',
  `thumb_url` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '封面url',
  `thumb_resource_id` bigint(20) DEFAULT NULL COMMENT '封面对应的文件id',
  `author` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '作者',
  `digest` varchar(512) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '摘要',
  `url` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '原文链接',
  `content_source_url` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '点击图文的原文链接跳转的链接',
  `content` longtext CHARACTER SET utf8mb4 COMMENT '原文html内容',
  `description` text CHARACTER SET utf8mb4 COMMENT '描述',
  `sort` int(11) DEFAULT NULL COMMENT '排序，越小越前',
  `created_at` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间',
  `created_ip` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL,
  `recommend` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '推荐语',
  `is_original` tinyint(3) DEFAULT NULL COMMENT '是否原创',
  `is_share` tinyint(3) DEFAULT NULL COMMENT '是否共享',
  `is_deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `create_time` datetime DEFAULT NULL COMMENT '文章创建/发布时间',
  `update_time` datetime DEFAULT NULL COMMENT '文章上次更新时间',
  `thumb_url_11` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '1:1封面url',
  `state` tinyint(3) DEFAULT '1' COMMENT '文章状态1正常，0已删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_appid_mid` (`appid`,`mid`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号上的文章';

-- ----------------------------
-- Table structure for wx_article_comment
-- ----------------------------
DROP TABLE IF EXISTS `wx_article_comment`;
CREATE TABLE `wx_article_comment` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间',
  `is_deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `msg_data_id` varchar(32) DEFAULT NULL COMMENT '冗余',
  `article_id` bigint(20) DEFAULT NULL COMMENT '文章id',
  `user_comment_id` int(11) NOT NULL COMMENT '用户评论id ',
  `openid` varchar(60) DEFAULT NULL COMMENT 'openid，用户如果用非微信身份评论，不返回openid',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `content` text COMMENT '评论内容 ',
  `comment_type` tinyint(3) DEFAULT NULL COMMENT '是否精选评论，0为即非精选，1为true，即精选',
  `author_reply_time` datetime DEFAULT NULL COMMENT '作者回复时间',
  `author_reply_content` text COMMENT '作者回复内容 ',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_main` (`article_id`,`msg_data_id`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章留言表';

-- ----------------------------
-- Table structure for wx_article_msg_link
-- ----------------------------
DROP TABLE IF EXISTS `wx_article_msg_link`;
CREATE TABLE `wx_article_msg_link` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `msg_data_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'msg_data_id',
  `article_id` bigint(20) NOT NULL COMMENT 'wx_article_id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_main` (`article_id`,`msg_data_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信文章和推送的msg_data_id关联';

-- ----------------------------
-- Table structure for wx_article_sync_config
-- ----------------------------
DROP TABLE IF EXISTS `wx_article_sync_config`;
CREATE TABLE `wx_article_sync_config` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间',
  `is_deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `appid` varchar(40) NOT NULL COMMENT '微信appid',
  `expect_date` date DEFAULT NULL COMMENT '期望同步到的日期',
  `last_sync_date` date DEFAULT NULL COMMENT '上次同步到的日期',
  `last_sync_max_date` date DEFAULT NULL COMMENT '上次同步到的最大日期',
  `enable` tinyint(3) DEFAULT NULL COMMENT '是否启用',
  `default_job_num` int(11) DEFAULT '0' COMMENT '默认同步数量（每日）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信文章同步配置表';

-- ----------------------------
-- Table structure for wx_cfg
-- ----------------------------
DROP TABLE IF EXISTS `wx_cfg`;
CREATE TABLE `wx_cfg` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `pubtype` tinyint(11) DEFAULT NULL COMMENT '公众号类型：0,未认证订阅号;1,未认证服务号;2,认证订阅号;3,认证服务号',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_cfg_copy1
-- ----------------------------
DROP TABLE IF EXISTS `wx_cfg_copy1`;
CREATE TABLE `wx_cfg_copy1` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `pubtype` tinyint(11) DEFAULT NULL COMMENT '公众号类型：0,未认证订阅号;1,未认证服务号;2,认证订阅号;3,认证服务号',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_daily_info
-- ----------------------------
DROP TABLE IF EXISTS `wx_daily_info`;
CREATE TABLE `wx_daily_info` (
  `id` bigint(20) NOT NULL COMMENT '公众号日常统计',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `ref_date` date DEFAULT NULL COMMENT '统计日期',
  `cumulate_user` int(11) DEFAULT '0' COMMENT '粉丝人数',
  `new_user` int(11) DEFAULT '0' COMMENT '新增粉丝',
  `cancel_user` int(11) DEFAULT '0' COMMENT '减少粉丝',
  `int_page_read_user` int(11) DEFAULT '0' COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT '0' COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT '0' COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT '0' COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT '0' COMMENT '分享的人数',
  `share_count` int(11) DEFAULT '0' COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT '0' COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT '0' COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_event
-- ----------------------------
DROP TABLE IF EXISTS `wx_event`;
CREATE TABLE `wx_event` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(12) DEFAULT NULL,
  `create_time` int(11) DEFAULT NULL,
  `events` varchar(64) DEFAULT NULL,
  `event_key` varchar(128) DEFAULT NULL,
  `from_user_name` varchar(128) DEFAULT NULL,
  `latitude` varchar(16) DEFAULT NULL,
  `longitude` varchar(16) DEFAULT NULL,
  `msg_type` varchar(64) DEFAULT NULL,
  `precisions` varchar(128) DEFAULT NULL,
  `ticket` varchar(1024) DEFAULT NULL,
  `to_user_name` varchar(128) DEFAULT NULL,
  `menu_id` varchar(128) DEFAULT NULL,
  `msg_id` varchar(32) DEFAULT NULL,
  `status` varchar(32) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `FromUserName` (`from_user_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_event_subs
-- ----------------------------
DROP TABLE IF EXISTS `wx_event_subs`;
CREATE TABLE `wx_event_subs` (
  `id` bigint(20) NOT NULL COMMENT '微信事件订阅',
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `wx_cfg_id` bigint(20) DEFAULT NULL COMMENT '微信公众号配置id',
  `events` varchar(1024) DEFAULT NULL COMMENT '事件 subscribe,unsubscribe,LOCATION,CLICK,VIEW',
  `url` varchar(1024) DEFAULT NULL COMMENT '转发目的url',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_keyword
-- ----------------------------
DROP TABLE IF EXISTS `wx_keyword`;
CREATE TABLE `wx_keyword` (
  `id` bigint(11) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL COMMENT '公众号id',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id',
  `type` varchar(16) DEFAULT NULL COMMENT '匹配类型, equal, contains, RegExp',
  `keyword` varchar(128) DEFAULT NULL COMMENT '关键字',
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `keyword` (`keyword`) USING BTREE,
  KEY `type` (`type`,`keyword`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`,`keyword`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_media
-- ----------------------------
DROP TABLE IF EXISTS `wx_media`;
CREATE TABLE `wx_media` (
  `id` bigint(20) unsigned NOT NULL COMMENT '所有内容都以此表建立系统内media_id, 图文, 多图文, 图片, 视频, 音乐, 语音',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信媒体类型',
  `name` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `media_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'media_id',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `is_expired` tinyint(4) DEFAULT NULL COMMENT '是否过期',
  `wx_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信返回的url',
  `wx_created_at` timestamp NULL DEFAULT NULL COMMENT '微信返回的创建时间',
  `title` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '标题',
  `description` text CHARACTER SET utf8 COMMENT '描述',
  `expired_type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '素材类型：perm永久，temp临时',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` text CHARACTER SET utf8,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `type_2` (`type`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_media_log
-- ----------------------------
DROP TABLE IF EXISTS `wx_media_log`;
CREATE TABLE `wx_media_log` (
  `id` bigint(20) unsigned NOT NULL COMMENT '素材同步记录',
  `user_id` bigint(20) DEFAULT NULL,
  `acct_id` bigint(20) DEFAULT NULL,
  `is_success` tinyint(4) DEFAULT NULL COMMENT '是否成功',
  `success_num` int(11) DEFAULT NULL COMMENT '成功数量',
  `media_num` int(11) DEFAULT NULL COMMENT '同步总数量',
  `media_type` varchar(16) DEFAULT NULL COMMENT '素材类型',
  `created_ip` varchar(1600) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_media_recycle
-- ----------------------------
DROP TABLE IF EXISTS `wx_media_recycle`;
CREATE TABLE `wx_media_recycle` (
  `id` bigint(11) unsigned NOT NULL COMMENT '素材回收站',
  `acct_id` bigint(11) DEFAULT NULL COMMENT '公众号id',
  `type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信媒体类型',
  `name` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `media_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'media_id',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `is_expired` tinyint(4) DEFAULT NULL COMMENT '是否过期',
  `wx_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信返回的url',
  `wx_created_at` timestamp NULL DEFAULT NULL COMMENT '微信返回的创建时间',
  `title` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '标题',
  `description` text CHARACTER SET utf8 COMMENT '描述',
  `expired_type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '素材类型：perm永久，temp临时',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `recycle_at` datetime DEFAULT NULL COMMENT '进入回收站时间',
  `memo` text CHARACTER SET utf8,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `type_2` (`type`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_menu
-- ----------------------------
DROP TABLE IF EXISTS `wx_menu`;
CREATE TABLE `wx_menu` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(12) DEFAULT NULL COMMENT '公众号id',
  `app_id` varchar(32) DEFAULT NULL,
  `menu` varchar(4096) DEFAULT NULL COMMENT '菜单内容',
  `menu_last` varchar(4096) DEFAULT NULL,
  `menu_self` varchar(4096) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `create_ip` varchar(16) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_msg
-- ----------------------------
DROP TABLE IF EXISTS `wx_msg`;
CREATE TABLE `wx_msg` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(12) DEFAULT NULL,
  `to_user_name` varchar(128) DEFAULT NULL COMMENT '接收者',
  `from_user_name` varchar(128) DEFAULT NULL COMMENT '发送者',
  `msg_type` varchar(64) DEFAULT NULL COMMENT '消息类型',
  `create_time` int(12) DEFAULT NULL,
  `msg_id` varchar(1024) DEFAULT NULL COMMENT '消息id',
  `content` varchar(1024) DEFAULT NULL COMMENT '内容',
  `description` varchar(1024) DEFAULT NULL COMMENT '描述',
  `format` varchar(64) DEFAULT NULL,
  `label` varchar(128) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `media_id` varchar(1024) DEFAULT NULL,
  `pic_url` varchar(1024) DEFAULT NULL,
  `scale` varchar(64) DEFAULT NULL,
  `thumb_media_id` varchar(1024) DEFAULT NULL,
  `title` varchar(1024) DEFAULT NULL,
  `url` varchar(1024) DEFAULT NULL,
  `recognition` varchar(16) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `FromUserName` (`from_user_name`) USING BTREE,
  KEY `ToUserName` (`to_user_name`) USING BTREE,
  KEY `MsgId` (`msg_id`(255)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_news
-- ----------------------------
DROP TABLE IF EXISTS `wx_news`;
CREATE TABLE `wx_news` (
  `id` bigint(11) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL,
  `ref_id` bigint(11) DEFAULT NULL COMMENT 'id of wx_media',
  `ref_idx` bigint(11) DEFAULT NULL,
  `type` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `media_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `title` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `thumb_media_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `thumb_resource_id` bigint(20) DEFAULT NULL,
  `show_cover_pic` tinyint(4) DEFAULT NULL,
  `author` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `digest` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'type为news时表示图文的链接，type为linkNews时表示点击点击图文跳转的链接',
  `content_source_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'type为news时表示点击图文的原文链接跳转的链接，type为linkNews时表示缩略图的微信链接',
  `update_time` int(11) DEFAULT NULL,
  `content` longtext,
  `description` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `name` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `created_at` datetime DEFAULT NULL,
  `picture` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `created_ip` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) CHARACTER SET utf8 DEFAULT NULL,
  `recommend` varchar(1024) DEFAULT NULL COMMENT '推荐语',
  `is_original` tinyint(4) DEFAULT NULL COMMENT '是否原创',
  `is_share` tinyint(4) DEFAULT NULL COMMENT '是否共享',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`,`ref_id`,`ref_idx`) USING BTREE,
  KEY `ref_id` (`ref_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_program_cfg
-- ----------------------------
DROP TABLE IF EXISTS `wx_program_cfg`;
CREATE TABLE `wx_program_cfg` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `pubtype` tinyint(11) DEFAULT NULL COMMENT '公众号类型：0,未认证订阅号;1,未认证服务号;2,认证订阅号;3,认证服务号',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  `file_id` varchar(255) DEFAULT NULL COMMENT '业务域名配置文件',
  `story_code` varchar(1024) DEFAULT NULL COMMENT '小程序体验版二维码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

-- ----------------------------
-- Table structure for wx_push_details
-- ----------------------------
DROP TABLE IF EXISTS `wx_push_details`;
CREATE TABLE `wx_push_details` (
  `id` bigint(20) NOT NULL COMMENT '图文群发统计详情',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `ref_date` date DEFAULT NULL COMMENT '推文日期',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `msgid` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图文消息id_索引(如：10000050_1)',
  `title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图文消息的标题',
  `user_source` int(11) DEFAULT NULL,
  `target_user` int(11) DEFAULT NULL COMMENT '送达人数',
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `int_page_from_session_read_user` int(11) DEFAULT NULL COMMENT '公众号会话阅读人数',
  `int_page_from_session_read_count` int(11) DEFAULT NULL COMMENT '公众号会话阅读次数',
  `int_page_from_hist_msg_read_user` int(11) DEFAULT NULL COMMENT '历史消息页阅读人数',
  `int_page_from_hist_msg_read_count` int(11) DEFAULT NULL COMMENT '历史消息页阅读次数',
  `int_page_from_feed_read_user` int(11) DEFAULT NULL COMMENT '朋友圈阅读人数',
  `int_page_from_feed_read_count` int(11) DEFAULT NULL COMMENT '朋友圈阅读次数',
  `int_page_from_friends_read_user` int(11) DEFAULT NULL COMMENT '好友转发阅读人数',
  `int_page_from_friends_read_count` int(11) DEFAULT NULL COMMENT '好友转发阅读次数',
  `int_page_from_other_read_user` int(11) DEFAULT NULL COMMENT '其他场景阅读人数',
  `int_page_from_other_read_count` int(11) DEFAULT NULL COMMENT '其他场景阅读次数',
  `feed_share_from_session_user` int(11) DEFAULT NULL COMMENT '公众号会话转发朋友圈人数',
  `feed_share_from_session_cnt` int(11) DEFAULT NULL COMMENT '公众号会话转发朋友圈次数',
  `feed_share_from_feed_user` int(11) DEFAULT NULL COMMENT '朋友圈转发朋友圈人数',
  `feed_share_from_feed_cnt` int(11) DEFAULT NULL COMMENT '朋友圈转发朋友圈次数',
  `feed_share_from_other_user` int(11) DEFAULT NULL COMMENT '其他场景转发朋友圈人数',
  `feed_share_from_other_cnt` int(11) DEFAULT NULL COMMENT '其他场景转发朋友圈次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_push_increment
-- ----------------------------
DROP TABLE IF EXISTS `wx_push_increment`;
CREATE TABLE `wx_push_increment` (
  `id` bigint(20) NOT NULL COMMENT '图文群发每日统计数据(增量)',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `msgid` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图文消息id_索引(如：10000050_1)',
  `title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图文消息的标题',
  `user_source` int(11) DEFAULT NULL,
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_push_statistics
-- ----------------------------
DROP TABLE IF EXISTS `wx_push_statistics`;
CREATE TABLE `wx_push_statistics` (
  `id` bigint(20) NOT NULL COMMENT '图文群发统计详情',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `ref_date` date DEFAULT NULL COMMENT '数据日期',
  `user_source` int(11) DEFAULT NULL COMMENT '0:会话;1.好友;2.朋友圈;3.腾讯微博;4.历史消息页;5.其他;6.看一看;7.搜一搜',
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_push_total
-- ----------------------------
DROP TABLE IF EXISTS `wx_push_total`;
CREATE TABLE `wx_push_total` (
  `id` bigint(20) NOT NULL COMMENT '图文群发统计总数',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `url` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'url',
  `ref_date` date DEFAULT NULL COMMENT '推文日期',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `msgid` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图文消息id_索引(如：10000050_1)',
  `title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图文消息的标题',
  `target_user` int(11) DEFAULT NULL COMMENT '送达人数',
  `user_source` int(11) DEFAULT NULL,
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_qrcode
-- ----------------------------
DROP TABLE IF EXISTS `wx_qrcode`;
CREATE TABLE `wx_qrcode` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL,
  `action_name` varchar(64) DEFAULT NULL COMMENT '二维码类型，QR_SCENE为临时的整型参数值，QR_STR_SCENE为临时的字符串参数值，QR_LIMIT_SCENE为永久的整型参数值，QR_LIMIT_STR_SCENE为永久的字符串参数值',
  `scene_id` int(12) DEFAULT NULL COMMENT '业务id',
  `scene_str` varchar(128) DEFAULT NULL COMMENT '业务字符串',
  `expire_seconds` int(12) DEFAULT NULL COMMENT '该二维码有效时间，以秒为单位。 最大不超过2592000（即30天），此字段如果不填，则默认有效期为30秒。',
  `ticket` varchar(512) DEFAULT NULL COMMENT '获取的二维码ticket，凭借此ticket可以在有效时间内换取二维码',
  `url` varchar(512) DEFAULT NULL,
  `qr_url` varchar(512) DEFAULT NULL COMMENT '二维码图片解析后的地址，开发者可根据该地址自行生成需要的二维码图片',
  `expired_type` varchar(16) DEFAULT NULL COMMENT '类型：perm永久，temp临时',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sence_id` (`scene_id`) USING BTREE,
  KEY `scene_str` (`scene_str`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_resource
-- ----------------------------
DROP TABLE IF EXISTS `wx_resource`;
CREATE TABLE `wx_resource` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `acct_id` int(11) DEFAULT NULL,
  `type` varchar(16) DEFAULT NULL,
  `media_id` varchar(128) DEFAULT NULL,
  `title` varchar(256) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `url` varchar(1024) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `create_at` int(11) DEFAULT NULL,
  `ref_id` int(11) DEFAULT NULL COMMENT 'id of wx_media',
  `ref_idx` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `media_id` (`media_id`) USING BTREE,
  KEY `acct_id` (`acct_id`,`ref_id`,`ref_idx`) USING BTREE,
  KEY `ref_id` (`ref_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_rule
-- ----------------------------
DROP TABLE IF EXISTS `wx_rule`;
CREATE TABLE `wx_rule` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `acct_id` bigint(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL COMMENT '规则名称',
  `rule_type` varchar(32) DEFAULT NULL COMMENT '规则类型  keyword,subscribe,default',
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL COMMENT '附件描述',
  `way` varchar(32) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_rule_media
-- ----------------------------
DROP TABLE IF EXISTS `wx_rule_media`;
CREATE TABLE `wx_rule_media` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `media_id` bigint(20) DEFAULT NULL COMMENT '媒体id',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `rule_id` (`rule_id`) USING BTREE,
  KEY `media_id` (`media_id`,`rule_id`) USING BTREE,
  KEY `media_id_2` (`media_id`,`rule_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_tags
-- ----------------------------
DROP TABLE IF EXISTS `wx_tags`;
CREATE TABLE `wx_tags` (
  `id` bigint(100) unsigned NOT NULL,
  `acct_id` bigint(20) DEFAULT NULL,
  `tag_id` int(11) DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  `count` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_user_phone_record
-- ----------------------------
DROP TABLE IF EXISTS `wx_user_phone_record`;
CREATE TABLE `wx_user_phone_record` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `appid` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信appid',
  `openid` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信openid',
  `phone` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `appid_openid_phone` (`openid`,`appid`,`phone`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户-登录手机记录';

-- ----------------------------
-- Table structure for wx_user_tags
-- ----------------------------
DROP TABLE IF EXISTS `wx_user_tags`;
CREATE TABLE `wx_user_tags` (
  `id` bigint(40) unsigned NOT NULL,
  `tags_id` bigint(40) DEFAULT NULL,
  `user_id` bigint(40) DEFAULT NULL,
  `openid` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `tag_id` (`tags_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_userinfo
-- ----------------------------
DROP TABLE IF EXISTS `wx_userinfo`;
CREATE TABLE `wx_userinfo` (
  `id` bigint(20) NOT NULL,
  `openid` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号Id',
  `realname` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `nickname` varchar(191) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `mobile` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `email` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `idcard` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `sex` tinyint(4) DEFAULT NULL COMMENT '性别',
  `province` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `city` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `district` varchar(45) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `address` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `memo` mediumtext COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` datetime DEFAULT NULL,
  `create_on` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '创建时间',
  `status` tinyint(4) DEFAULT '0',
  `avatar` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `appid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `subscribe` int(11) DEFAULT NULL COMMENT '是否关注',
  `merchant_id` int(11) DEFAULT NULL COMMENT '商户id',
  `updated_on` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后登录ip',
  `subscribe_time` datetime DEFAULT NULL COMMENT '关注时间',
  `unsubscribe_time` datetime DEFAULT NULL COMMENT '取消关注时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL,
  `uid` bigint(20) DEFAULT NULL COMMENT '核心用户',
  `union_id` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信开发者平台唯一标识',
  `type` int(4) DEFAULT NULL COMMENT '0:公众号 2:小程序 3:网页应用',
  `remark` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信备注',
  `is_block` tinyint(4) DEFAULT '0' COMMENT '是否在黑名单',
  `block_at` datetime DEFAULT NULL COMMENT '拉黑时间',
  `temp` bigint(20) DEFAULT NULL COMMENT 'temp',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `openid` (`openid`(191)) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE,
  KEY `idcard` (`idcard`) USING BTREE,
  KEY `email` (`email`) USING BTREE,
  KEY `uid` (`uid`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for wx_website_cfg
-- ----------------------------
DROP TABLE IF EXISTS `wx_website_cfg`;
CREATE TABLE `wx_website_cfg` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `scope` varchar(20) DEFAULT NULL COMMENT 'scope',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

-- ----------------------------
-- Table structure for xsgc_business_members
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_business_members`;
CREATE TABLE `xsgc_business_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` bigint(20) DEFAULT '0' COMMENT '用户id',
  `realname` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '成员姓名',
  `mobile` varchar(16) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '手机号码',
  `type` int(8) DEFAULT NULL COMMENT '职位类型',
  `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  `status` tinyint(4) DEFAULT '1' COMMENT '有效状态，0-无效，1-有效',
  `pic` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '图片id列表',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`) USING BTREE,
  KEY `mobile` (`mobile`) USING BTREE,
  KEY `idx_deletedat_realname_userid` (`is_deleted`,`realname`,`user_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1713011162638675969 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-业务人员';

-- ----------------------------
-- Table structure for xsgc_business_members_relate
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_business_members_relate`;
CREATE TABLE `xsgc_business_members_relate` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `member_id` bigint(20) DEFAULT NULL COMMENT '成员id',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户id',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1707935757157138433 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-业务人员客户关联表';

-- ----------------------------
-- Table structure for xsgc_business_members_type
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_business_members_type`;
CREATE TABLE `xsgc_business_members_type` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(64) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '职位名称',
  `desc` varchar(1024) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-业务人员职位';

-- ----------------------------
-- Table structure for xsgc_customer_info
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_customer_info`;
CREATE TABLE `xsgc_customer_info` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `number` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户编码',
  `name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '客户名称',
  `status` tinyint(4) DEFAULT '0' COMMENT '当前状态,0无效，1-有效',
  `start_date` date DEFAULT NULL COMMENT '服务开始日期',
  `end_date` date DEFAULT NULL COMMENT '服务结束日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `option_id` bigint(20) DEFAULT NULL COMMENT '套餐id',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `contact_user` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(13) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人电话',
  `contact_email` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系人邮箱',
  `memo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `logo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo',
  `renewal_mark` tinyint(4) DEFAULT '0' COMMENT '续签标记，0-首次开通,1-续签,2-临时续签',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-客户信息表';

-- ----------------------------
-- Table structure for xsgc_customer_service_record
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_customer_service_record`;
CREATE TABLE `xsgc_customer_service_record` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `customer_id` bigint(20) DEFAULT NULL COMMENT '客户id',
  `active` tinyint(4) DEFAULT '0' COMMENT '是否激活，0-未激活，1-已激活',
  `start_date` date DEFAULT NULL COMMENT '服务开始日期',
  `end_date` date DEFAULT NULL COMMENT '服务结束日期',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `option_id` bigint(20) DEFAULT NULL COMMENT '套餐id',
  `organization_id` bigint(20) DEFAULT NULL COMMENT '组织id',
  `memo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '套餐说明',
  `renewal_mark` tinyint(4) DEFAULT '0' COMMENT '续签标记，0-首次开通,1-续签,2-临时续签',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-客户服务开通记录';

-- ----------------------------
-- Table structure for xsgc_message_remind
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_message_remind`;
CREATE TABLE `xsgc_message_remind` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `project_id` bigint(20) DEFAULT NULL COMMENT '工程id',
  `type` tinyint(4) DEFAULT NULL COMMENT '消息类型，1-预约安装提醒，2-预约回收提醒，3-现场勘察提醒，4-上门安装提醒，5-上门回收提醒，6-已接入监管的提醒，7-结束监管的提醒，8-违规告警的提醒',
  `relate_id` bigint(20) DEFAULT NULL COMMENT '关联id，1~7是关联monitor_flow的id, 8是关联aiot_event的id',
  `status` tinyint(4) DEFAULT '0' COMMENT '是否已读,0-未读，1-已读',
  `content` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '消息内容',
  `memo` varchar(1000) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注信息，json字符串格式',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-消息提醒表';

-- ----------------------------
-- Table structure for xsgc_organization
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_organization`;
CREATE TABLE `xsgc_organization` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `name` varchar(30) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '组织名称',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-组织';

-- ----------------------------
-- Table structure for xsgc_subscription
-- ----------------------------
DROP TABLE IF EXISTS `xsgc_subscription`;
CREATE TABLE `xsgc_subscription` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `name` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '套餐名称',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `created_user` bigint(20) DEFAULT NULL COMMENT '创建人',
  `updated_user` bigint(20) DEFAULT NULL COMMENT '修改人',
  `memo` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '套餐说明',
  `status` tinyint(4) DEFAULT '0' COMMENT '套餐状态, 0-无效， 1-有效',
  `type` tinyint(4) DEFAULT '0' COMMENT '套餐类型, 0-低级套餐， 1-高级套餐',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='小散工程-套餐';

-- ----------------------------
-- View structure for v_construction_unit_statistics
-- ----------------------------
DROP VIEW IF EXISTS `v_construction_unit_statistics`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `v_construction_unit_statistics` AS select `cu`.`id` AS `id`,`cu`.`name` AS `name`,`cu`.`admin_name` AS `admin_name`,`cu`.`admin_mobile` AS `admin_mobile`,`cu`.`status` AS `status`,`cu`.`created_at` AS `created_at`,`cuor`.`organization_id` AS `organization_id`,`cuor`.`credit_score` AS `credit_score`,`cuor`.`is_blacklist` AS `is_blacklist`,`cuor`.`blacklist_reason` AS `blacklist_reason`,`cuor`.`blacklist_time` AS `blacklist_time`,coalesce(`worker_stats`.`worker_count`,0) AS `worker_count`,coalesce(`leader_stats`.`leader_count`,0) AS `leader_count`,coalesce(`project_stats`.`total_project_count`,0) AS `total_project_count`,coalesce(`project_stats`.`ongoing_project_count`,0) AS `ongoing_project_count`,coalesce(`alert_stats`.`alert_count`,0) AS `alert_count`,0 AS `violation_count`,0 AS `rectification_count` from (((((`db_tn_sc047f71a5d4ef1134`.`construction_unit` `cu` join `db_tn_sc047f71a5d4ef1134`.`construction_unit_organization_relate` `cuor` on((`cu`.`id` = `cuor`.`construction_unit_id`))) left join (select `sp`.`contractor` AS `contractor`,`sp`.`organization_id` AS `organization_id`,count(distinct `em`.`id`) AS `worker_count` from ((`db_tn_sc047f71a5d4ef1134`.`sporadic_project` `sp` left join `db_tn_sc047f71a5d4ef1134`.`engineering_members_project_relate` `empr` on(((cast(`sp`.`id` as char charset utf8mb4) = cast(`empr`.`project_id` as char charset utf8mb4)) and (`empr`.`is_deleted` = 0)))) left join `db_tn_sc047f71a5d4ef1134`.`engineering_members` `em` on(((`empr`.`member_id` = `em`.`id`) and (`em`.`type` = 3) and (`em`.`is_deleted` = 0)))) where (`sp`.`is_deleted` = 0) group by `sp`.`contractor`,`sp`.`organization_id`) `worker_stats` on((((`cu`.`name` collate utf8mb4_unicode_ci) = (`worker_stats`.`contractor` collate utf8mb4_unicode_ci)) and (`cuor`.`organization_id` = `worker_stats`.`organization_id`)))) left join (select `sp`.`contractor` AS `contractor`,`sp`.`organization_id` AS `organization_id`,count(distinct `em`.`id`) AS `leader_count` from ((`db_tn_sc047f71a5d4ef1134`.`sporadic_project` `sp` left join `db_tn_sc047f71a5d4ef1134`.`engineering_members_project_relate` `empr` on(((cast(`sp`.`id` as char charset utf8mb4) = cast(`empr`.`project_id` as char charset utf8mb4)) and (`empr`.`is_deleted` = 0)))) left join `db_tn_sc047f71a5d4ef1134`.`engineering_members` `em` on(((`empr`.`member_id` = `em`.`id`) and (`em`.`type` = 2) and (`em`.`is_deleted` = 0)))) where (`sp`.`is_deleted` = 0) group by `sp`.`contractor`,`sp`.`organization_id`) `leader_stats` on((((`cu`.`name` collate utf8mb4_unicode_ci) = (`leader_stats`.`contractor` collate utf8mb4_unicode_ci)) and (`cuor`.`organization_id` = `leader_stats`.`organization_id`)))) left join (select `db_tn_sc047f71a5d4ef1134`.`sporadic_project`.`contractor` AS `contractor`,`db_tn_sc047f71a5d4ef1134`.`sporadic_project`.`organization_id` AS `organization_id`,count(0) AS `total_project_count`,sum((case when (`db_tn_sc047f71a5d4ef1134`.`sporadic_project`.`status` = 1) then 1 else 0 end)) AS `ongoing_project_count` from `db_tn_sc047f71a5d4ef1134`.`sporadic_project` where (`db_tn_sc047f71a5d4ef1134`.`sporadic_project`.`is_deleted` = 0) group by `db_tn_sc047f71a5d4ef1134`.`sporadic_project`.`contractor`,`db_tn_sc047f71a5d4ef1134`.`sporadic_project`.`organization_id`) `project_stats` on((((`cu`.`name` collate utf8mb4_unicode_ci) = (`project_stats`.`contractor` collate utf8mb4_unicode_ci)) and (`cuor`.`organization_id` = `project_stats`.`organization_id`)))) left join (select `sp`.`contractor` AS `contractor`,`sp`.`organization_id` AS `organization_id`,count(`ae`.`id`) AS `alert_count` from (`db_tn_sc047f71a5d4ef1134`.`sporadic_project` `sp` left join `db_tn_sc047f71a5d4ef1134`.`aiot_event` `ae` on(((cast(`sp`.`id` as char charset utf8mb4) = cast(`ae`.`project_id` as char charset utf8mb4)) and (`ae`.`is_deleted` = 0)))) where (`sp`.`is_deleted` = 0) group by `sp`.`contractor`,`sp`.`organization_id`) `alert_stats` on((((`cu`.`name` collate utf8mb4_unicode_ci) = (`alert_stats`.`contractor` collate utf8mb4_unicode_ci)) and (`cuor`.`organization_id` = `alert_stats`.`organization_id`)))) where ((`cu`.`is_deleted` = 0) and (`cuor`.`is_deleted` = 0));

-- ----------------------------
-- Procedure structure for create_db_tn_sc047f71a5d4ef1134_table
-- ----------------------------
DROP PROCEDURE IF EXISTS `create_db_tn_sc047f71a5d4ef1134_table`;
delimiter ;;
CREATE PROCEDURE `create_db_tn_sc047f71a5d4ef1134_table`()
BEGIN

CREATE TABLE IF NOT EXISTS `com_counter`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '计数器',
  `ref_id` bigint(20) NULL DEFAULT NULL COMMENT '来源id',
  `refer` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源，ncms_articles, cms_comment, ncms_attachment, ncms_category, ncms_specialcolumn, ncms_word_hot, ncms_tags, ncms_word_source, ncms_word_sensitive',
  `counter` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '计数器类型或叫名称，page,user, upvote, comment, favor, share, read, article, serach',
  `count` int(11) NULL DEFAULT 0 COMMENT '计数值',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '最后操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `ref_id`(`ref_id`, `refer`, `counter`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `com_dictionary`  (
  `id` bigint(20) NOT NULL COMMENT '数据字典',
  `refer` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表',
  `refer_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `ref_clo` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列',
  `ref_clo_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '列名',
  `p_id` bigint(20) NULL DEFAULT 0 COMMENT '父级节点',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '键',
  `value` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '值',
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `enabled` tinyint(4) NULL DEFAULT 1 COMMENT '是否锁定',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_org`  (
  `id` bigint(20) NOT NULL COMMENT '组织机构表',
  `brief` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '摘要／描述',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一编码',
  `org_id` bigint(20) NULL DEFAULT 0 COMMENT '父级节点',
  `type_id` bigint(20) NULL DEFAULT NULL COMMENT '组织机构编号',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '机构名称',
  `disable` tinyint(4) DEFAULT '0' COMMENT '是否隐藏',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `sort` int(20) NULL DEFAULT 0,
  `p_id` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code`(`code`) USING BTREE,
  INDEX `org_id`(`org_id`) USING BTREE,
  INDEX `title`(`title`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_org_user`  (
  `id` bigint(20) NOT NULL,
  `org_id` bigint(20) NULL DEFAULT NULL COMMENT '组织编号',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户编号',
  `job` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '职务',
  `sort` int(11) DEFAULT NULL COMMENT '排序值',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `org_id_user_id`(`org_id`, `user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_orgtag`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '组织架构的用户角色',
  `p_id` bigint(20) NULL DEFAULT 0 COMMENT '父级id',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一识别码',
  `brief` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '是否有效',
  `sort` int(20) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '人员标签' ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_orgtag_user`  (
  `id` bigint(20) UNSIGNED NOT NULL,
  `org_tag_id` bigint(20) NULL DEFAULT NULL,
  `user_id` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_orgtype`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '组织机构type',
  `brief` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '显示名称',
  `code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一key',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '是否有效',
  `sort` bigint(20) NULL DEFAULT 0,
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '组织机构类型' ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_region`  (
  `id` bigint(20) NOT NULL COMMENT '行政区划',
  `p_id` bigint(20) NOT NULL DEFAULT 1 COMMENT '父级ID',
  `type` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类型， province, city, district, subdistrict, community, neighborhood, residential community',
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `brief` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简述',
  `cover` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `resource_id` bigint(20) NULL DEFAULT NULL COMMENT '图片资源编号',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '是否开启',
  `pid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sort` int(20) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`p_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `com_upload_resource`  (
  `id` bigint(20) NOT NULL,
  `userid` int(11) NULL DEFAULT NULL COMMENT '用户ID',
  `name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `path` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件相对路径',
  `type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'mime类型',
  `size` int(11) NULL DEFAULT NULL COMMENT '文件大小',
  `file_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '原文件名',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `create_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `remote_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件绝对路径',
  `local_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保存在服务器的文件路径',
  `remark` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `memo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `thumb` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `parent_id` int(11) NULL DEFAULT NULL,
  `type_id` int(11) NULL DEFAULT NULL,
  `account_id` int(11) NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `file_name`(`name`) USING BTREE,
  INDEX `local_url`(`local_url`(255)) USING BTREE,
  INDEX `path`(`path`(255)) USING BTREE,
  INDEX `remote_url`(`remote_url`(255)) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `file_upload_resource` (
  `id` bigint(20) unsigned NOT NULL,
  `userid` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `name` varchar(1024) DEFAULT NULL COMMENT '文件名',
  `path` varchar(1024) DEFAULT NULL COMMENT '文件相对路径',
  `type` varchar(128) DEFAULT NULL COMMENT 'mime类型',
  `file_size` int(11) DEFAULT NULL COMMENT '文件大小',
  `file_name` varchar(1024) DEFAULT NULL COMMENT '原文件名',
  `file_length` bigint(20) DEFAULT NULL COMMENT '媒体文件时长',
  `storage_type` tinyint(4) DEFAULT NULL COMMENT '存储类型: 0. oss 1. minio 2. 外部资源',
  `relative_path` varchar(1024) DEFAULT NULL COMMENT '文件相对路径',
  `access_control` tinyint(4) DEFAULT NULL COMMENT '访问权限: 0. oss 1. minio',
  `remote_url` varchar(1024) DEFAULT NULL COMMENT '文件绝对路径',
  `local_url` varchar(1024) DEFAULT NULL COMMENT '保存在服务器的文件路径',
  `minio_url` varchar(1024) DEFAULT NULL COMMENT '迁移后保存至minio的文件路径',
  `remark` varchar(1024) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `memo` varchar(1024) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '上传者id',
  `thumb` varchar(1024) DEFAULT NULL,
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父级id',
  `type_id` int(11) DEFAULT NULL,
  `create_ip` varchar(16) DEFAULT NULL,
  `account_id` bigint(20) DEFAULT NULL,
  `signature` varchar(64) DEFAULT NULL COMMENT '服务端签名',
  `created_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `oss_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '按租户隔离后迁移至oss的url',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `file_name` (`name`) USING BTREE,
  KEY `path` (`path`(255)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
CREATE TABLE IF NOT EXISTS `gis_fence`  (
  `id` bigint(20) NOT NULL DEFAULT 0 COMMENT '区域块/线路',
  `type` tinyint(4) NULL DEFAULT NULL COMMENT '类型 0.线路 1.圆 2.多边形',
  `points` varchar(5000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经纬度集合经纬度之间用逗号隔开，一组经纬度之间用分号隔开',
  `lng` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '圆心经度',
  `lat` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '圆心纬度',
  `radius` int(16) NULL DEFAULT NULL COMMENT '圆形围栏半径（米）',
  `name` varchar(265) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `valid_time` datetime(0) NULL DEFAULT NULL COMMENT '有效时间',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `area` float NULL DEFAULT NULL COMMENT '面积',
  `author` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据添加者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `gis_poi`  (
  `id` bigint(20) NOT NULL DEFAULT 0 COMMENT '区域块/线路',
  `lat` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
  `lng` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `memo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `valid_time` datetime(0) NULL DEFAULT NULL COMMENT '有效时间',
  `author` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据添加者',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `ncms_article_special`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '文章与专题关联表',
  `article_id` bigint(20) NULL DEFAULT NULL COMMENT '文章ID',
  `special_id` bigint(20) NULL DEFAULT NULL COMMENT '专题ID',
  `created_at` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0),
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `article_id2`(`article_id`, `special_id`) USING BTREE,
  INDEX `article_id`(`article_id`) USING BTREE,
  INDEX `category_id`(`special_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE `ncms_fusion_check_cfg` (
  `id` bigint(20) NOT NULL,
  `need_check` tinyint(1) DEFAULT NULL COMMENT '是否需要审核，0否，1是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
CREATE TABLE IF NOT EXISTS `ncms_article_tags`  (
  `id` bigint(20) NOT NULL,
  `article_id` bigint(20) NOT NULL COMMENT '文章ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `created_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `article_id_2`(`article_id`, `tag_id`) USING BTREE,
  INDEX `article_id`(`article_id`) USING BTREE,
  INDEX `tag_id`(`tag_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE `ncms_articles` (
  `id` bigint(20) unsigned NOT NULL COMMENT '文章id',
  `type` tinyint(4) DEFAULT '1' COMMENT '文章类型,0内容图文,1链接图文,2坐标图文',
  `category_id` bigint(20) DEFAULT NULL,
  `category_ids` varchar(128) DEFAULT NULL,
  `user_id` bigint(20) DEFAULT NULL COMMENT '发布者',
  `source_id` bigint(20) DEFAULT NULL COMMENT '来源',
  `author` varchar(128) DEFAULT NULL COMMENT '作者',
  `title` varchar(1024) DEFAULT NULL COMMENT '文章标题',
  `digest` varchar(1024) DEFAULT NULL COMMENT '文章摘要',
  `picture` varchar(1024) DEFAULT NULL COMMENT '封面',
  `content` longtext COMMENT '文章内容',
  `active` tinyint(4) DEFAULT '1' COMMENT '状态   0不显示  1显示',
  `last_viewed` datetime DEFAULT NULL COMMENT '最后访问时间',
  `refer_url` varchar(1024) DEFAULT NULL COMMENT '关联链接',
  `is_prime` tinyint(4) DEFAULT '0' COMMENT '精华帖,1是,0不是,',
  `anonymous` tinyint(4) DEFAULT '0' COMMENT '是否匿名 1是 0不是',
  `poi_id` bigint(20) DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `address` varchar(256) DEFAULT NULL COMMENT '地址',
  `status` tinyint(4) DEFAULT '0' COMMENT '审核状态0.待审核 1.已审核 2.退回 3.已归档 4.出档 5.审核中 6.已发布 7.草稿',
  `sort` int(11) DEFAULT NULL,
  `is_trend` tinyint(4) DEFAULT '0' COMMENT '热点 1:是 0否',
  `is_topped` tinyint(4) DEFAULT '0' COMMENT '置顶：0否，1是',
  `is_original` tinyint(4) DEFAULT '0' COMMENT '是否原创：0否，1是',
  `is_recommend` int(4) DEFAULT NULL COMMENT '推荐，默认0',
  `is_share` tinyint(4) DEFAULT '0' COMMENT '是否允许共享，1允许，0不允许',
  `is_show_cover` tinyint(4) DEFAULT '0' COMMENT '封面是否显示于正文',
  `share_type` tinyint(4) DEFAULT NULL COMMENT '共享类型，0未共享，1内部，2区内',
  `memo` varchar(255) DEFAULT NULL,
  `cids` varchar(255) DEFAULT NULL,
  `mask_id` varchar(255) DEFAULT NULL,
  `platform_id` bigint(20) DEFAULT NULL COMMENT '发布之后，同步至平台端的id',
  `main_id` bigint(20) DEFAULT NULL COMMENT '发布主体 id',
  `created_ip` varchar(128) DEFAULT NULL COMMENT '创建IP',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `release_at` datetime DEFAULT NULL COMMENT '发布时间',
  `updated_at` datetime DEFAULT NULL COMMENT '最近更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
CREATE TABLE IF NOT EXISTS `ncms_attachment`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '文章图片',
  `user_id` bigint(20) NULL DEFAULT NULL,
  `article_id` bigint(20) NULL DEFAULT NULL COMMENT '文章id',
  `type` enum('ARTICLE','INTERACTION') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型，文章类型，article，评论(互动)类型，interaction',
  `file_id` bigint(20) NULL DEFAULT NULL COMMENT '文件Id，可用来下载照片文件',
  `file_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '照片名称',
  `file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文件路径url（云储备上获取）',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '状态 1显示，0隐藏',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `article_id`(`article_id`) USING BTREE,
  INDEX `file_id`(`file_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `ncms_articles_participate` (
  `id` bigint(20) NOT NULL COMMENT '记录查看过文章的用户',
  `user_id` bigint(20) DEFAULT NULL,
  `article_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
CREATE TABLE IF NOT EXISTS `ncms_category`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '栏目说明',
  `pid` bigint(20) NULL DEFAULT 0 COMMENT '父级版块，默认0',
  `pids` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版块名称',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版块的英文名，全表唯一，在前台的URL中进行拼接',
  `logo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'logo，栏目内容图',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '栏目说明',
  `active` tinyint(4) NULL DEFAULT 0 COMMENT '激活状态，默认激活，0否，1是',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'url',
  `permission_id` bigint(11) NULL DEFAULT NULL COMMENT '权限id',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '板块图标',
  `meta_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Meta关键字',
  `meta_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Meta标题',
  `meta_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Meta描述',
  `is_upvote` tinyint(4) NULL DEFAULT NULL COMMENT '是否支持点赞，0否，1是',
  `is_audit` tinyint(4) NULL DEFAULT NULL COMMENT '是否需要审核，0否，1是',
  `is_comment` tinyint(4) NULL DEFAULT NULL COMMENT '是否支持评论，0否，1是',
  `is_recommend` tinyint(4) NULL DEFAULT NULL COMMENT '是否推荐，0否，1是',
  `cids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `parent_id`(`pid`) USING BTREE,
  INDEX `name`(`display_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `ncms_category_user-y`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '用户拥有的栏目',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '后台用户',
  `category_id` bigint(20) NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `ncms_cu_permission-y`  (
  `id` bigint(20) NOT NULL COMMENT '用户的分类的权限记录表',
  `cu_id` bigint(20) NULL DEFAULT NULL COMMENT 'ncms_category_user的编号',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `ncms_datasource`  (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `driver_class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT 10000 COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT 10000 COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT 100 COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT 100 COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT 18800 COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT 300000 COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否开启',
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展示名',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
CREATE TABLE IF NOT EXISTS `ncms_ext_data`  (
  `id` bigint(20) NOT NULL COMMENT '接收爬虫收集的文章',
  `article_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章编号',
  `author_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者编号',
  `mask_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'varchar',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `author` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `digest` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '概要',
  `title` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'URL',
  `readed` tinyint(4) NULL DEFAULT NULL COMMENT '是否阅读',
  `cover_img_url` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '封面图',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `attachments` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '附件数组(废弃字段)',
  `memo` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预留字段',
  `syn_id` bigint(4) NULL DEFAULT NULL COMMENT 'ncms_articles判断是否同步',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `tnt_art_uk`(`article_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_ext_type`  (
  `id` bigint(20) NOT NULL COMMENT '爬虫项目类型',
  `project` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `user_id` bigint(20) NULL DEFAULT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT NULL,
  `created_at` datetime(0) NOT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`project`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;
INSERT INTO `ncms_ext_type` VALUES (1, 'mei_pian', 1, '美篇', 1, '2018-01-26 17:22:16', NULL, NULL);
INSERT INTO `ncms_ext_type` VALUES (2, 'tou_tiao', 1, '头条', 1, '2018-09-03 11:17:07', NULL, NULL);

CREATE TABLE IF NOT EXISTS `ncms_interaction`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '关注／收藏 / 点赞',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `itype` enum('COMMENT','UPVOTE','CLICK','EVALUATE','FAVOR','READ','SHARE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\'COMMENT\',\'UPVOTE\',\'CLICK\',\'EVALUATE\',\'FAVOR\',\'READ\',\'SHARE\'',
  `pid` bigint(20) NULL DEFAULT NULL COMMENT '父id',
  `article_id` bigint(20) NULL DEFAULT NULL COMMENT '文章id',
  `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评论内容',
  `reply` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回复',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '审核状态,0否，1是',
  `score` int(11) NULL DEFAULT NULL COMMENT '评分',
  `poi_id` bigint(20) NULL DEFAULT NULL COMMENT '位置id',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建ip地址',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间，关注时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间，通知时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间，取消关注',
  `article_type` tinyint(255) DEFAULT NULL COMMENT '阅读/点赞/收藏/评论过的文章类型；1表示文章，2表示视频，3表示活动',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `article_id`(`article_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_specialcolumn`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '专题',
  `pid` bigint(20) NULL DEFAULT 0 COMMENT '父级版块，默认0',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专题名称',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '版块的英文名，全表唯一，在前台的URL中进行拼接',
  `logo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'logo',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '专题描述',
  `active` tinyint(4) NULL DEFAULT 0 COMMENT '激活状态，默认激活，0否，1是',
  `sort` int(11) NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '板块图标',
  `is_recommend` tinyint(4) NULL DEFAULT 0 COMMENT '是否推荐，0否，1是',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `parent_id`(`pid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_tags`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '标签定义分类',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类描述',
  `active` tinyint(4) NULL DEFAULT 1 COMMENT '激活状态， 默认激活，0否，是',
  `count` int(11) NULL DEFAULT NULL COMMENT '保留',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_user_account`  (
  `id` bigint(20) NOT NULL COMMENT '融媒体账号',
  `author_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者编号',
  `status` tinyint(4) NULL DEFAULT 1 COMMENT '1，启用／0，停用',
  `author` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `account_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '账号',
  `atype_id` bigint(20) NULL DEFAULT NULL COMMENT '账号类型',
  `url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '与用户绑定的首页链接',
  `cat_id` bigint(20) NULL DEFAULT NULL COMMENT '同步栏目',
  `cat_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `source_id` bigint(20) NULL DEFAULT NULL COMMENT '来源编号',
  `url_type` tinyint(4) NULL DEFAULT NULL COMMENT '使用原文URL，0否，1是',
  `auto_syn` tinyint(4) NULL DEFAULT NULL COMMENT '是否自动同步到图文，0否，1是',
  `memo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `art_url` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `count` int(11) NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '增加的时间',
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_user_counter`  (
  `id` bigint(11) UNSIGNED NOT NULL,
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '前端用户',
  `refer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源',
  `ref_id` bigint(20) NULL DEFAULT NULL COMMENT '来源表的id',
  `counter` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型',
  `count` int(255) NULL DEFAULT NULL COMMENT '点击数',
  `update_at` datetime(0) NULL DEFAULT NULL COMMENT '最后操作时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_user_tags`  (
  `id` bigint(20) NOT NULL,
  `uacc_id` bigint(20) NULL DEFAULT NULL COMMENT 'user_account  表id',
  `tag_id` bigint(20) NULL DEFAULT NULL COMMENT '标签id',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_word_hot`  (
  `id` bigint(20) NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '热词名称',
  `search_times` int(10) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '搜索次数',
  `order_at` int(10) NULL DEFAULT NULL COMMENT '排序',
  `active` tinyint(4) UNSIGNED ZEROFILL NULL DEFAULT 0001 COMMENT '是否推荐，0否，1是',
  `count` int(11) NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_word_sensitive`  (
  `id` bigint(20) NOT NULL,
  `badword` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '敏感词',
  `active` tinyint(4) NULL DEFAULT NULL COMMENT '是否启用，0否，1是',
  `replacement` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '替换',
  `rank` tinyint(4) NULL DEFAULT NULL COMMENT '层级',
  `source` enum('TENANT','PLATFORM') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源',
  `platform_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台端被同步敏感词id',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `created`(`created_at`) USING BTREE,
  INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_word_source`  (
  `id` bigint(20) NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '来源名称',
  `state` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源说明',
  `active` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否有效，0否，1是',
  `count` int(11) NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `ncms_operate_success_log` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志类型',
  `sub_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志子类型',
  `user_id` bigint(20) DEFAULT NULL COMMENT '系统用户id',
  `action` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志内容',
  `biz_no` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务标识(被操作对象的业务编号)',
  `extra` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '额外',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `type_subtype_aid` (`type`,`sub_type`,`user_id`,`biz_no`) USING BTREE COMMENT '在什么类型下的子类型谁的操作',
  KEY `biz_no` (`type`,`sub_type`,`biz_no`) USING BTREE COMMENT '某个被操作的对象'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='CMS操作成功日志';

CREATE TABLE IF NOT EXISTS `ncms_user_category_link` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_at` datetime DEFAULT NULL COMMENT '关联时间',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `category_ids` varchar(255) DEFAULT NULL COMMENT '关联的分类id集合（包含每一个node）',
  `category_names` varchar(255) DEFAULT NULL COMMENT '关联的分类名',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `user_id` (`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户与文章分类关联表';

CREATE TABLE IF NOT EXISTS `pubshare_approval_log`  (
  `id` bigint(20) NOT NULL COMMENT '稿件审核',
  `sid` bigint(20) NULL DEFAULT NULL COMMENT '资源id',
  `operation` tinyint(4) NULL DEFAULT NULL COMMENT '审核状态，0待审核，1通过，2拒绝',
  `suggestion` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核意见',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '审批用户id',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注\r\n',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_config`  (
  `id` int(10) UNSIGNED NOT NULL,
  `cnf_key` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'key保存配置名，\r\nvalue保存配置值。pubshare_config投稿配置（0所有单位1选定单位3不对外投稿）',
  `cnf_value` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '配置的值。可以定为0-1-2',
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `pubshare_config` VALUES (1, 'accept_config', '0', NULL, NULL, NULL);
INSERT INTO `pubshare_config` VALUES (2, 'accept_config', '1', NULL, NULL, NULL);
INSERT INTO `pubshare_config` VALUES (3, 'accept_config', '2', NULL, NULL, NULL);
INSERT INTO `pubshare_config` VALUES (4, 'send_config', '0', NULL, NULL, NULL);
INSERT INTO `pubshare_config` VALUES (5, 'send_config', '1', NULL, NULL, NULL);
INSERT INTO `pubshare_config` VALUES (6, 'send_config', '2', NULL, NULL, NULL);

CREATE TABLE IF NOT EXISTS `pubshare_config_list`  (
  `id` bigint(20) NOT NULL,
  `op_type` tinyint(4) NULL DEFAULT NULL COMMENT '操作标识，0表示发送，1表示接受',
  `op_tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '白名单或者黑名单的租户ID',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `account_id` bigint(20) NULL DEFAULT NULL COMMENT '当前商户id',
  `config_id` bigint(20) NULL DEFAULT NULL COMMENT 'pubshare_config表id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_datasource`  (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `driver_class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT 10000 COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT 10000 COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT 100 COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT 100 COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT 18800 COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT 300000 COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否开启',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展示名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_interaction`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT '关注／收藏 / 点赞',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `itype` enum('COMMENT','UPVOTE','CLICK','EVALUATE','FAVOR','READ') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\'COMMENT\',\'UPVOTE\',\'CLICK\',\'EVALUATE\',\'FAVOR\',\'READ\'',
  `pid` bigint(20) NULL DEFAULT NULL COMMENT '父id',
  `article_id` bigint(20) NULL DEFAULT NULL COMMENT '文章id',
  `content` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评论内容',
  `reply` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回复',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '审核状态,0否，1是',
  `score` int(11) NULL DEFAULT NULL COMMENT '评分',
  `poi_id` bigint(20) NULL DEFAULT NULL COMMENT '位置id',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建ip地址',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间，关注时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间，通知时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间，取消关注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `article_id`(`article_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_m_articles`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT 'cms接收',
  `type` tinyint(255) DEFAULT NULL COMMENT '文章类型,0内容图文,1链接图文,2坐标图文',
  `source` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源',
  `author` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `title` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章标题',
  `digest` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '文章摘要',
  `active` tinyint(4) NULL DEFAULT 1 COMMENT '状态   0不显示  1显示',
  `picture_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封面resource_upload的id',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `created_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建IP',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '最近更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `refer_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联链接',
  `poi_id` bigint(20) NULL DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `share_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'sharelist表的id',
  `address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `status` tinyint(4) NULL DEFAULT 0 COMMENT '审核状态0.待审核 1.已审核 2.退回 3.已归档 4.出档 5.审核中 6. 已发布',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_m_resource`  (
  `id` bigint(20) NOT NULL COMMENT '共享资源表',
  `type` enum('PICTURE','VIDEO','AUDIO','FILE','TEMPLATE','STYLE','PERSONALTEMPLATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\'PICTURE\',\'VIDEO\',\'MUSIC\',\'FILE\'',
  `resource_id` bigint(20) NULL DEFAULT NULL COMMENT '资源id',
  `author` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `user_id` bigint(20) NULL DEFAULT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资源名称',
  `active` tinyint(4) NULL DEFAULT NULL COMMENT '是否显示，0否，1是',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `poi_id` bigint(20) NULL DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `share_id` varchar(20) DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `created_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_m_wx_media`  (
  `id` bigint(11) UNSIGNED NOT NULL COMMENT '所有内容都以此表建立系统内media_id, 图文, 多图文, 图片, 视频, 音乐, 语音',
  `acct_id` bigint(11) NULL DEFAULT NULL COMMENT '公众号id',
  `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信媒体类型',
  `name` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `media_id` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'media_id',
  `resource_id` bigint(20) NULL DEFAULT NULL COMMENT '资源id',
  `is_expired` tinyint(4) NULL DEFAULT NULL COMMENT '是否过期',
  `wx_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '微信返回的url',
  `wx_created_at` timestamp(0) NULL DEFAULT NULL COMMENT '微信返回的创建时间',
  `title` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '描述',
  `expired_type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '素材类型：perm永久，temp临时',
  `share_id` varchar(20) DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `created_ip` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `memo` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `acct_id`(`acct_id`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `type_2`(`type`) USING BTREE,
  INDEX `acct_id_2`(`acct_id`, `type`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_m_wx_news`  (
  `id` bigint(11) UNSIGNED NOT NULL,
  `acct_id` bigint(11) NULL DEFAULT NULL,
  `ref_id` bigint(11) NULL DEFAULT NULL COMMENT 'id of wx_media',
  `ref_idx` bigint(11) NULL DEFAULT NULL,
  `type` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `media_id` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `title` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `thumb_media_id` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `thumb_resource_id` bigint(20) NULL DEFAULT NULL,
  `show_cover_pic` tinyint(4) NULL DEFAULT NULL,
  `author` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `digest` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `content_source_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `update_time` int(11) NULL DEFAULT NULL,
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL,
  `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `picture` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `share_id` varchar(20) DEFAULT NULL,
  `created_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `memo` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `recommend` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '推荐语',
  `is_original` tinyint(4) NULL DEFAULT NULL COMMENT '是否原创',
  `is_share` tinyint(4) NULL DEFAULT NULL COMMENT '是否共享',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `acct_id`(`acct_id`, `ref_id`, `ref_idx`) USING BTREE,
  INDEX `ref_id`(`ref_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `pubshare_sharelist`  (
  `id` bigint(20) NOT NULL COMMENT '稿件审核',
  `resource_id` bigint(20) NULL DEFAULT NULL COMMENT '资源id',
  `related_msgid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联的微信稿件的msgid',
  `type` enum('WXNEWS','WXMEDIAL','CMS','RESOURCE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\'WXNEWS\',\'WXMEDIAL\',\'CMS\',\'RESOURCE\'',
  `material_type` enum('IMAGE','VIDEO','VOICE','FILE','TEXT') CHARACTER SET utf8mb4 COLLATE utf8mb4_german2_ci NULL DEFAULT NULL COMMENT '\'IMAGE\',\'VIDEO\',\'VOICE\',\'FILE\',\'TEXT\'',
  `title` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '稿件名称',
  `is_push` tinyint(4) NULL DEFAULT NULL COMMENT '收发类型，0收，1发',
  `tenant_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户名称',
  `scid` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '投稿人id',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '投稿人',
  `phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '内容',
  `score` double(20, 0) NULL DEFAULT NULL COMMENT '评分',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '审核状态，0待审核，1通过，2拒绝',
  `approval_suggestion` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '审核意见',
  `approval_at` datetime(0) NULL DEFAULT NULL COMMENT '审批时间',
  `approval_by` bigint(20) NULL DEFAULT NULL COMMENT '审批用户id',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注\r\n',
  `read_count` int(11) NULL DEFAULT 0 COMMENT '关联微信稿件的阅读量',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_category`  (
  `id` bigint(25) UNSIGNED NOT NULL COMMENT 'id',
  `pid` bigint(20) NULL DEFAULT 0 COMMENT '父级分类，默认0',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称，全表唯一，在前台的URL中进行拼接',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类的英文名',
  `tab` enum('PICTURE','VIDEO','AUDIO','FILE','TEMPLATE','STYLE','PERSONALTEMPLATE','PERSONALPICTURE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\'PICTURE\',\'VIDEO\',\'AUDIO\',\'FILE\',\'TEMPLATE\',\'STYLE\',\'PERSONALTEMPLATE\',\'PERSONALPICTURE\'',
  `type` enum('PLATFORM','TENANT') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台端platform，商户端tenant',
  `logo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'logo',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类描述',
  `user_id` bigint(25) NULL DEFAULT NULL COMMENT '用户id',
  `tenant_id` bigint(25) NULL DEFAULT NULL COMMENT '商户id',
  `tier` tinyint(255) NULL DEFAULT NULL COMMENT '当前分类的层级',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `status` tinyint(4) NULL DEFAULT 0 COMMENT '激活状态，默认激活，可见',
  `sort` int(11) NULL DEFAULT NULL,
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `icon` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '分类图标',
  `meta_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `meta_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `meta_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_upvote` tinyint(4) NULL DEFAULT NULL COMMENT '是否支持点赞',
  `is_audit` tinyint(4) NULL DEFAULT NULL COMMENT '是否需要审核',
  `is_comment` tinyint(4) NULL DEFAULT NULL COMMENT '是否支持评论',
  `is_recommend` tinyint(4) NULL DEFAULT NULL,
  `sync_id` bigint(25) NULL DEFAULT NULL COMMENT '租户端同步平台端的资源项id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `parent_id`(`pid`) USING BTREE,
  INDEX `displayname`(`display_name`) USING BTREE,
  INDEX `sync_id`(`sync_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `resource_category` VALUES (1, 0, '图片', NULL, NULL, 'TENANT', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (2, 0, '视频', NULL, NULL, 'TENANT', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (3, 0, '音频', NULL, NULL, 'TENANT', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (4, 0, '文件', NULL, NULL, 'TENANT', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (5, 0, '类型', NULL, NULL, 'TENANT', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (6, 0, '模板', NULL, NULL, 'TENANT', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (11, 0, '图片', NULL, NULL, 'PLATFORM', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (12, 0, '视频', NULL, NULL, 'PLATFORM', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (13, 0, '音频', NULL, NULL, 'PLATFORM', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (14, 0, '文件', NULL, NULL, 'PLATFORM', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (15, 0, '类型', NULL, NULL, 'PLATFORM', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `resource_category` VALUES (16, 0, '模板', NULL, NULL, 'PLATFORM', NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

CREATE TABLE IF NOT EXISTS `resource_datasource`  (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `driver_class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT 10000 COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT 10000 COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT 100 COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT 100 COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT 18800 COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT 300000 COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否开启',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展示名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_item`  (
  `id` bigint(20) UNSIGNED NOT NULL COMMENT 'cms接收',
  `type` enum('PICTURE','VIDEO','AUDIO','FILE','TEMPLATE','STYLE','PERSONALTEMPLATE') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '\'PICTUER\',\'VIDEO\',\'AUDIO\',\'FILE\',\'TEMPLATE\',\'STYLE\',\'PERSONALTEMPLATE\'（个人模板）',
  `category_id` bigint(20) NULL DEFAULT NULL,
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '发布者',
  `source` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源',
  `author` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '作者',
  `title` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `active` tinyint(4) NULL DEFAULT 1 COMMENT '状态   0不显示  1显示',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `created_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '创建IP',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '最近更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `refer_url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联链接',
  `is_prime` tinyint(4) NULL DEFAULT 0 COMMENT '精华帖,1是,0不是,',
  `anonymous` tinyint(4) NULL DEFAULT 0 COMMENT '是否匿名 1是 0不是',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '文章内容',
  `poi_id` bigint(20) NULL DEFAULT NULL COMMENT '地图点位的id，gis_pois',
  `address` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `status` tinyint(4) NULL DEFAULT 0 COMMENT '审核状态0.待审核 1.已审核 2.退回 3.已归档 4.出档 5.审核中 6. 已发布',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `digest` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '摘要',
  `is_trend` tinyint(4) NULL DEFAULT 0 COMMENT '热点 1:是 0否',
  `is_push` tinyint(4) NULL DEFAULT NULL COMMENT '必推，0否，1是',
  `is_topped` tinyint(4) NULL DEFAULT 0 COMMENT '置顶，1：置顶，0：非置顶',
  `is_original` tinyint(4) NULL DEFAULT 0 COMMENT '是否原创',
  `is_recommend` int(4) NULL DEFAULT NULL COMMENT '推荐，默认0',
  `is_share` tinyint(4) NULL DEFAULT 0 COMMENT '是否允许共享，1允许，0不允许',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `cids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_favor` tinyint(255) NULL DEFAULT NULL COMMENT '是否收藏，0否，1是',
  `source_id` bigint(20) NULL DEFAULT NULL COMMENT '上传资源id',
  `route` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类的父级id',
  `sid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '爬虫平台提供的唯一ID标示',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `sync_id` bigint(25) NULL DEFAULT NULL COMMENT '租户端同步平台端的资源项id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `inx_user_id`(`id`, `user_id`) USING BTREE,
  INDEX `source_id`(`source_id`) USING BTREE,
  INDEX `sync_id`(`sync_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_item_account`  (
  `id` bigint(20) NOT NULL,
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT '资源id',
  `account_id` bigint(20) NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `item_id`(`item_id`) USING BTREE,
  INDEX `account_id`(`account_id`) USING BTREE,
  INDEX `item_account`(`item_id`, `account_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_item_tag`  (
  `id` bigint(20) NOT NULL,
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT ' 资源id',
  `tag_id` bigint(20) NULL DEFAULT NULL COMMENT '标签id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `item_id`(`item_id`) USING BTREE,
  INDEX `tag_id`(`tag_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_item_user`  (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT '资源项id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `category_id` bigint(20) NULL DEFAULT NULL COMMENT '修改后的分类id',
  `is_favor` tinyint(4) NULL DEFAULT NULL COMMENT '是否收藏  0否  1是',
  `is_topped` tinyint(4) NULL DEFAULT NULL COMMENT '是否置顶，0否  1是',
  `update_title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资源项标题',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间(置顶)',
  `favor_updated_at` datetime(0) NULL DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `inx_item_user_favor`(`item_id`, `user_id`, `is_favor`) USING BTREE,
  INDEX `item_id`(`item_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_item_user_count`  (
  `id` bigint(20) NOT NULL,
  `counter_id` bigint(20) NULL DEFAULT NULL COMMENT '统计数据的id',
  `item_id` bigint(20) NULL DEFAULT NULL COMMENT '资源项id',
  `tenant_id` bigint(20) NULL DEFAULT NULL COMMENT '商户id',
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户id',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `version` bigint(20) NULL DEFAULT 0 COMMENT '搜索次数',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `resource_tag`  (
  `id` bigint(20) NOT NULL,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签名称',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_app`  (
  `id` bigint(20) NOT NULL COMMENT '应用',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用名称',
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用标识',
  `icon` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用图标',
  `biref` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用简介',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '有效状态',
  `created_at` datetime(0) NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_datasource`  (
  `id` bigint(20) NOT NULL DEFAULT 0,
  `driver_class_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'com.mysql.jdbc.Driver',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `max_active` int(11) NOT NULL DEFAULT 10000 COMMENT '配置最大连接',
  `max_wait` int(11) NOT NULL DEFAULT 10000 COMMENT '连接等待超时时间',
  `min_idle` int(11) NOT NULL DEFAULT 100 COMMENT '配置最小连接',
  `initial_size` int(11) NOT NULL DEFAULT 100 COMMENT '配置初始连接',
  `eviction_time` bigint(20) NOT NULL DEFAULT 18800 COMMENT '间隔多久进行检测,关闭空闲连接 毫秒',
  `min_live` int(11) NOT NULL DEFAULT 300000 COMMENT '一个连接最小生存时间 毫秒',
  `is_active` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否开启',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `code` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '唯一名称',
  `display_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '展示名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_ext`  (
  `id` bigint(20) NOT NULL COMMENT '编号',
  `key_id` bigint(20) NULL DEFAULT NULL COMMENT '键',
  `value` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '键的值',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `key_id`(`key_id`) USING BTREE,
  INDEX `val_id`(`value`(255)) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_ext_key`  (
  `id` bigint(20) NOT NULL,
  `label` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '键名',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `label`(`label`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_menu`  (
  `id` bigint(20) NOT NULL,
  `name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单标识',
  `display_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '菜单展示名称',
  `icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单的icon',
  `parent_id` bigint(20) NULL DEFAULT NULL COMMENT '菜单父id',
  `sort` int(20) NOT NULL DEFAULT 0 COMMENT '排序',
  `route` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `memo` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `active` tinyint(4) NULL DEFAULT 1 COMMENT '状态(1:正常,0:停用)',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `url` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由',
  `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单版本',
  `sidebar` tinyint(4) DEFAULT NULL COMMENT '是否现在在菜单列表中',
  `active_icon` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '激活图标',
  `payload` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '扩展字段',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_menu_permission`  (
  `id` bigint(20) NOT NULL,
  `menu_id` bigint(20) NOT NULL COMMENT '菜单id',
  `permission_id` bigint(20) NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `menu_permission_unique`(`menu_id`, `permission_id`) USING BTREE,
  INDEX `permission_id_NORMAL`(`permission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_operate_log`  (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NULL DEFAULT NULL COMMENT '操作员',
  `created_ip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` datetime(0) NULL DEFAULT NULL,
  `updated_at` datetime(0) NULL DEFAULT NULL,
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作说明',
  `module` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '操作模块',
  `agent` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `mark` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标识',
  `url` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求',
  `param` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '参数',
  `result` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '结果',
  `user_type` tinyint(4) NULL DEFAULT NULL COMMENT '用户类型 1.管理端 2.微信端',
  `ip` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'ip',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_permission_role`  (
  `id` bigint(20) NOT NULL,
  `permission_id` bigint(20) UNSIGNED NOT NULL COMMENT '权限id',
  `role_id` bigint(20) UNSIGNED NOT NULL COMMENT '角色id',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `permission_role_unique`(`permission_id`, `role_id`) USING BTREE,
  INDEX `role_id_foreign`(`role_id`) USING BTREE,
  INDEX `permission_id_foreign`(`permission_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '权限与用户组角色对应关系表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_permissions`  (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限名',
  `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '权限展示名',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详情',
  `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `active` tinyint(4) NULL DEFAULT 1 COMMENT '是否开启',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '权限信息表' ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_role_user`  (
  `id` bigint(20) NOT NULL,
  `user_id` bigint(20) NOT NULL COMMENT '菜单id',
  `role_id` bigint(20) NOT NULL COMMENT '权限id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKbxq0ovvpqxj20etjklj85hhs4`(`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `sys_roles`  (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `display_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '角色展示名',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '角色描述',
  `created_at` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '修改更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `type` tinyint(4) NULL DEFAULT NULL COMMENT '角色类型 1.系统 2.商户',
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '资源标识',
  `active` tinyint(4) NULL DEFAULT NULL COMMENT '是否开启',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户组角色表' ROW_FORMAT = Dynamic;

CREATE TABLE `sys_users` (
  `id` bigint(20) NOT NULL,
  `username` varchar(256) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户登录名',
  `password` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户密码',
  `nickname` varchar(256) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户屏显昵称，可以不同用户登录名',
  `email` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '邮箱',
  `realname` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户真实姓名',
  `pid` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号',
  `pid_card_thumb1` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证证件正面（印有国徽图案、签发机关和有效期限）照片',
  `pid_card_thumb2` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证证件反面（印有个人基本信息和照片）照片',
  `avatar` varchar(512) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '用户个人图像',
  `phone` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机号码',
  `address` varchar(150) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '联系地址',
  `emergency_contact` varchar(300) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '紧急联系人信息',
  `servicer_id` int(12) DEFAULT '0' COMMENT '专属客服id，（为0表示其为无专属客服的管理用户）',
  `deleted_at` datetime DEFAULT NULL COMMENT '被软删除时间',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改更新时间',
  `is_lock` tinyint(3) DEFAULT '0' COMMENT '是否锁定限制用户登录，1锁定,0正常',
  `confirmation_code` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '确认码',
  `confirmed` tinyint(1) DEFAULT '0' COMMENT '是否已通过验证 0：未通过 1：通过',
  `remember_token` varchar(60) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'Laravel 追加的记住令牌',
  `info` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
  `sex` tinyint(4) DEFAULT NULL COMMENT '性别：1，男；2，女；3，保密',
  `job_number` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '工号',
  `enterprise_email` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '企业邮箱',
  `location` varchar(120) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '办公地点',
  `birth` datetime DEFAULT NULL COMMENT '生日',
  `sign` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '个人签名',
  `region_id` bigint(20) DEFAULT NULL COMMENT '地区',
  `qr` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信二维码',
  `type` tinyint(4) DEFAULT '1' COMMENT '用户类型  0.游客 1.后台 2.微信 ',
  `verified` tinyint(4) DEFAULT NULL COMMENT '实名认证 0.未审核 1.通过 2.未通过 3.取消',
  `verify_time` datetime DEFAULT NULL COMMENT '申请实名认证时间',
  `verified_at` datetime DEFAULT NULL COMMENT '认证时间',
  `remark` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '审核意见',
  `last_time` datetime DEFAULT NULL COMMENT '最后一次访问时间',
  `last_ip` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '最后一次访问ip',
  `login_times` int(11) DEFAULT NULL COMMENT '登录次数',
  PRIMARY KEY (`id`),
  KEY `user_nickname_index` (`nickname`(191)),
  KEY `user_phone_index` (`phone`),
  KEY `user_username_idk` (`username`(191)),
  KEY `user_pid_ink` (`pid`),
  KEY `user_email_ink` (`email`),
  KEY `user_realname_index` (`realname`(191)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
CREATE TABLE IF NOT EXISTS `sys_usr_login_log` (
 `id` bigint(20) NOT NULL,
 `user_id` bigint(20) DEFAULT NULL COMMENT '用户编号',
 `username` varchar(256) DEFAULT NULL COMMENT '用户名',
 `phone` varchar(256) DEFAULT NULL COMMENT '手机号码',
 `os` varchar(128) NOT NULL DEFAULT '' COMMENT '操作系统',
 `engine` varchar(20) NOT NULL DEFAULT '' COMMENT '引擎',
 `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '平台来源 wx/ali/sc',
 `way` varchar(255) NOT NULL DEFAULT '' COMMENT '登录方式',
 `client_type` varchar(20) DEFAULT '' COMMENT '客户端类型 app/browser/mp',
 `client` varchar(20) NOT NULL COMMENT '客户端 browser/wx_browser/wx_gov/wx_mp/ilh_app/isz_app/ali_mp',
 `user_agent` varchar(1024) DEFAULT NULL COMMENT 'userAgent',
 `referer` varchar(1024) DEFAULT NULL COMMENT 'referer',
 `event` varchar(20) DEFAULT NULL COMMENT '事件 login logout ',
 `memo` varchar(255) DEFAULT NULL COMMENT '备注',
 `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '1.成功 0.失败',
 `created_at` datetime NOT NULL COMMENT '创建时间',
 `created_ip` varchar(256) NOT NULL COMMENT '创建ip',
 `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
 `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
 `is_deleted` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否被删除',
 PRIMARY KEY (`id`) USING BTREE,
 KEY `uid_idx` (`user_id`,`is_deleted`) USING BTREE,
 KEY `name_idx` (`username`,`is_deleted`) USING BTREE,
 KEY `ct_idx` (`client_type`,`is_deleted`) USING BTREE,
 KEY `w_idx` (`way`,`is_deleted`) USING BTREE,
 KEY `client_idx` (`client`,`is_deleted`) USING BTREE,
 KEY `phone_idx` (`phone`,`is_deleted`) USING BTREE,
 KEY `ip_idx` (`created_ip`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `sys_user_config` (
  `id` bigint(20) NOT NULL COMMENT 'id',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `business_key` varchar(100) DEFAULT NULL COMMENT 'key-业务名称',
  `business_value` varchar(256) DEFAULT NULL COMMENT 'value-配置项',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '修改时间',
  `is_deleted` tinyint(4) DEFAULT '0' COMMENT '是否删除，0-否，1-是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户配置表';

CREATE TABLE IF NOT EXISTS `sms_msg_account`  (
    `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '短信账号表',
    `account` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '账号',
    `password` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
    `type` tinyint(4) NULL DEFAULT NULL COMMENT '类型 1:云之讯 2.阿里云 3.无线',
    `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
    `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
    `param_json` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '其余参数',
    `is_active` tinyint(4) NULL DEFAULT 0 COMMENT '是否开启',
    `url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '请求链接',
    `memo` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `sort` int(12) NULL DEFAULT NULL COMMENT '顺序',
    `send_uri` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送接口',
    `batch_send_uri` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '批量发送接口',
    `add_template_uri` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '增加模板接口',
    `del_template_uri` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除模板接口',
    `update_template_uri` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '修改模板接口',
    `get_template_uri` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '获取模板接口',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `sms_msg_account` VALUES (2, '860205', 'ZHCFJ6G66U', 3, '2017-06-03 10:54:07', '2017-06-03 10:54:09', NULL, '{\"custCode\":\"860205\",\"custPwd\":\"ZHCFJ6G66U\",\"needReport\":\"yes\"}', 1, 'https://smsapp.wlwx.com/sendSms', '0', 1, 'https://smsapp.wlwx.com/sendSms', NULL, NULL, NULL, NULL, NULL);
INSERT INTO `sms_msg_account` VALUES (7, '182899a07f2075b5322c5fe29e259906', '18b6eb21db972fff1eb8ee47f4ae40ad', 1, '2020-02-18 17:56:02', NULL, NULL, '{\"smsUtf8\":\"uft-8\",\"smsVersion\":\"2014-06-30\",\"smsAppid\":\"f8cfeb1dfd7e42e08566c74139f986ad\"}', 1, 'https://api.ucpaas.com', '0', 2, 'https://api.ucpaas.com/2014-06-30/Accounts/182899a07f2075b5322c5fe29e259906/Messages/templateSMS', NULL, NULL, NULL, NULL, NULL);

CREATE TABLE IF NOT EXISTS `back_ext` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `account_id` bigint(20) DEFAULT NULL,
  `value` varchar(256) DEFAULT NULL,
  `label` varchar(128) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `act_id` (`account_id`),
  KEY `val_id` (`value`(255))
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8;

INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('1', NULL, '', 'mng_config_name', NULL, '站点名称');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('2', NULL, '', 'mng_config_keyword', NULL, '关键字');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('3', NULL, '', 'mng_config_alias', NULL, '站点别名');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('4', NULL, '', 'mng_config_desc', NULL, '描述');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('5', NULL, '', 'mng_config_info', NULL, '域名信息');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('6', NULL, '', 'mng_config_copyright', NULL, '版权信息');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('7', NULL, '', 'mng_config_icon', NULL, '站点图标');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('8', NULL, '', 'mng_config_background', NULL, '站点背景');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('9', NULL, NULL, 'wx_mng_config_name', NULL, '移动端站点名称');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('10', NULL, NULL, 'wx_mng_config_keyword', NULL, '移动端关键字');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('11', NULL, NULL, 'wx_mng_config_alias', NULL, '移动端站点别名');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('12', NULL, NULL, 'wx_mng_config_desc', NULL, '移动端描述');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('13', NULL, NULL, 'wx_mng_config_info', NULL, '移动端域名信息');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('14', NULL, NULL, 'wx_mng_config_copyright', NULL, '移动端版权信息');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('15', NULL, NULL, 'wx_mng_config_icon', NULL, '移动端站点图标');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('16', NULL, NULL, 'wx_mng_config_background', NULL, '移动端站点背景');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('19', NULL, NULL, 'msg_servicer', NULL, '短信服务商');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('20', NULL, NULL, 'msg_month_limit', NULL, '短信每月限量');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('21', NULL, NULL, 'msg_suffix', NULL, '短信后缀');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('22', NULL, "minio", 'upload_mode', NULL, '上传模式');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('23', NULL, NULL, 'user_agreement', NULL, '《用户服务协议及隐私保护政策》');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('24', NULL, NULL, 'copyright_notice', NULL, '版权声明');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('25', NULL, NULL, 'law_notice', NULL, '法律声明');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('26', NULL, NULL, 'contact_us', NULL, '联系我们');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('27', NULL, NULL, 'website_help', NULL, '网站帮助');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('28', NULL, NULL, 'common_problem', NULL, '常见问题');
INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('29', NULL, "1", 'scan_login_active', NULL, '是否开启扫码登录');

INSERT INTO `back_ext` (`id`, `account_id`, `value`, `label`, `deleted_at`, `description`) VALUES ('30', NULL, "1", 'login_method', NULL, '登录方式（默认1-账密登录，2-二维码登录）');


CREATE TABLE IF NOT EXISTS `sms_msg_template`  (
    `id` bigint(20) NOT NULL COMMENT '短信模板',
    `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板名',
    `type` tinyint(4) NULL DEFAULT NULL COMMENT '模板类型 1:云之讯 2.阿里云 3.无线',
    `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
    `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '请求链接',
    `template_id` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模板编号',
    `state` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否可用',
    `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
    `refer` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联',
    `account_id` bigint(20) NULL DEFAULT NULL COMMENT 'sms_msg_account的编号',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `template_id`(`template_id`) USING BTREE,
    INDEX `refer`(`refer`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

INSERT INTO `sms_msg_template` VALUES (1, '后台管理登录验证码', 3, '您的验证码为：{0}，此验证码五分钟内有效。', 'http://123.58.255.70:8860/sendSms', NULL, 1, '2017-06-16 17:42:12', NULL, '2017-06-16 17:42:10', NULL, 'verify', 2);
INSERT INTO `sms_msg_template` VALUES (6, '发送短信', 1, '您的验证码为：{0}，此验证码5分钟内有效。', NULL, '535398', 1, '2020-03-14 14:30:51', NULL, NULL, NULL, 'verify', 7);

CREATE TABLE IF NOT EXISTS `sms_code_log`  (
    `id` bigint(20) NOT NULL,
    `user_id` bigint(20) NULL DEFAULT NULL COMMENT '用户编号',
    `mobile` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号码',
    `code` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验证码',
    `content` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送的内容',
    `resp` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '返回的内容',
    `source` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发送源',
    `create_on` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建ip',
    `remark` varchar(1024) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
    `created_at` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
    `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
    `template_id` bigint(20) NULL DEFAULT NULL COMMENT '模板编号',
    `account_id` bigint(20) NULL DEFAULT NULL COMMENT '账号编号',
    `status` tinyint(4) NULL DEFAULT 0 COMMENT '状态',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `wx_userinfo`  (
  `id` bigint(20) NOT NULL,
  `openid` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `acct_id` bigint(20) NULL DEFAULT NULL COMMENT '公众号Id',
  `realname` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `nickname` varchar(191) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `mobile` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `email` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `idcard` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `sex` tinyint(4) NULL DEFAULT NULL COMMENT '性别',
  `province` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `city` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `district` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `memo` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '备注',
  `created_at` datetime(0) NULL DEFAULT NULL,
  `create_on` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '创建时间',
  `status` tinyint(4) NULL DEFAULT 0,
  `avatar` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `appid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `subscribe` int(11) NULL DEFAULT NULL COMMENT '是否关注',
  `merchant_id` int(11) NULL DEFAULT NULL COMMENT '商户id',
  `updated_on` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '最后登录ip',
  `subscribe_time` datetime(0) NULL DEFAULT NULL COMMENT '关注时间',
  `unsubscribe_time` datetime(0) NULL DEFAULT NULL COMMENT '取消关注时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL,
  `uid` bigint(20) NULL DEFAULT NULL COMMENT '核心用户',
  `union_id` varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信开发者平台唯一标识',
  `type` int(4) DEFAULT NULL COMMENT '0:公众号 2:小程序 3:网页应用',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信备注',
  `is_block` tinyint(4) NULL DEFAULT 0 COMMENT '是否在黑名单',
  `block_at` datetime(0) NULL DEFAULT NULL COMMENT '拉黑时间',
  `temp` bigint(20) NULL DEFAULT NULL COMMENT 'temp',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `openid`(`openid`(191)) USING BTREE,
  INDEX `mobile`(`mobile`) USING BTREE,
  INDEX `idcard`(`idcard`) USING BTREE,
  INDEX `email`(`email`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

CREATE TABLE IF NOT EXISTS `wx_cfg` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `pubtype` tinyint(11) DEFAULT NULL COMMENT '公众号类型：0,未认证订阅号;1,未认证服务号;2,认证订阅号;3,认证服务号',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_daily_info` (
  `id` bigint(20) NOT NULL COMMENT '公众号日常统计',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `ref_date` date DEFAULT NULL COMMENT '统计日期',
  `cumulate_user` int(11) DEFAULT '0' COMMENT '粉丝人数',
  `new_user` int(11) DEFAULT '0' COMMENT '新增粉丝',
  `cancel_user` int(11) DEFAULT '0' COMMENT '减少粉丝',
  `int_page_read_user` int(11) DEFAULT '0' COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT '0' COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT '0' COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT '0' COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT '0' COMMENT '分享的人数',
  `share_count` int(11) DEFAULT '0' COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT '0' COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT '0' COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_event` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(12) DEFAULT NULL,
  `create_time` int(11) DEFAULT NULL,
  `events` varchar(64) DEFAULT NULL,
  `event_key` varchar(128) DEFAULT NULL,
  `from_user_name` varchar(128) DEFAULT NULL,
  `latitude` varchar(16) DEFAULT NULL,
  `longitude` varchar(16) DEFAULT NULL,
  `msg_type` varchar(64) DEFAULT NULL,
  `precisions` varchar(128) DEFAULT NULL,
  `ticket` varchar(1024) DEFAULT NULL,
  `to_user_name` varchar(128) DEFAULT NULL,
  `menu_id` varchar(128) DEFAULT NULL,
  `msg_id` varchar(32) DEFAULT NULL,
  `status` varchar(32) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(1024) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `FromUserName` (`from_user_name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_event_subs` (
  `id` bigint(20) NOT NULL COMMENT '微信事件订阅',
  `name` varchar(64) DEFAULT NULL COMMENT '名称',
  `wx_cfg_id` bigint(20) DEFAULT NULL COMMENT '微信公众号配置id',
  `events` varchar(1024) DEFAULT NULL COMMENT '事件 subscribe,unsubscribe,LOCATION,CLICK,VIEW',
  `url` varchar(1024) DEFAULT NULL COMMENT '转发目的url',
  `status` tinyint(4) DEFAULT NULL COMMENT '状态',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  `memo` varchar(256) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_keyword` (
  `id` bigint(11) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL COMMENT '公众号id',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id',
  `type` varchar(16) DEFAULT NULL COMMENT '匹配类型, equal, contains, RegExp',
  `keyword` varchar(128) DEFAULT NULL COMMENT '关键字',
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `keyword` (`keyword`) USING BTREE,
  KEY `type` (`type`,`keyword`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`,`keyword`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_media` (
  `id` bigint(20) unsigned NOT NULL COMMENT '所有内容都以此表建立系统内media_id, 图文, 多图文, 图片, 视频, 音乐, 语音',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信媒体类型',
  `name` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `media_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'media_id',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `is_expired` tinyint(4) DEFAULT NULL COMMENT '是否过期',
  `wx_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信返回的url',
  `wx_created_at` timestamp NULL DEFAULT NULL COMMENT '微信返回的创建时间',
  `title` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '标题',
  `description` text CHARACTER SET utf8 COMMENT '描述',
  `expired_type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '素材类型：perm永久，temp临时',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` text CHARACTER SET utf8,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `type_2` (`type`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_media_log` (
  `id` bigint(20) unsigned NOT NULL COMMENT '素材同步记录',
  `user_id` bigint(20) DEFAULT NULL,
  `acct_id` bigint(20) DEFAULT NULL,
  `is_success` tinyint(4) DEFAULT NULL COMMENT '是否成功',
  `success_num` int(11) DEFAULT NULL COMMENT '成功数量',
  `media_num` int(11) DEFAULT NULL COMMENT '同步总数量',
  `media_type` varchar(16) DEFAULT NULL COMMENT '素材类型',
  `created_ip` varchar(1600) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(256) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_media_recycle` (
  `id` bigint(11) unsigned NOT NULL COMMENT '素材回收站',
  `acct_id` bigint(11) DEFAULT NULL COMMENT '公众号id',
  `type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信媒体类型',
  `name` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `media_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'media_id',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '资源id',
  `is_expired` tinyint(4) DEFAULT NULL COMMENT '是否过期',
  `wx_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT '微信返回的url',
  `wx_created_at` timestamp NULL DEFAULT NULL COMMENT '微信返回的创建时间',
  `title` varchar(128) CHARACTER SET utf8 DEFAULT NULL COMMENT '标题',
  `description` text CHARACTER SET utf8 COMMENT '描述',
  `expired_type` varchar(16) CHARACTER SET utf8 DEFAULT NULL COMMENT '素材类型：perm永久，temp临时',
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `recycle_at` datetime DEFAULT NULL COMMENT '进入回收站时间',
  `memo` text CHARACTER SET utf8,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `type` (`type`) USING BTREE,
  KEY `type_2` (`type`) USING BTREE,
  KEY `acct_id_2` (`acct_id`,`type`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_menu` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(12) DEFAULT NULL COMMENT '公众号id',
  `app_id` varchar(32) DEFAULT NULL,
  `menu` varchar(4096) DEFAULT NULL COMMENT '菜单内容',
  `menu_last` varchar(4096) DEFAULT NULL,
  `menu_self` varchar(4096) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `create_ip` varchar(16) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `app_id` (`app_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_msg` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(12) DEFAULT NULL,
  `to_user_name` varchar(128) DEFAULT NULL COMMENT '接收者',
  `from_user_name` varchar(128) DEFAULT NULL COMMENT '发送者',
  `msg_type` varchar(64) DEFAULT NULL COMMENT '消息类型',
  `create_time` int(12) DEFAULT NULL,
  `msg_id` varchar(1024) DEFAULT NULL COMMENT '消息id',
  `content` varchar(1024) DEFAULT NULL COMMENT '内容',
  `description` varchar(1024) DEFAULT NULL COMMENT '描述',
  `format` varchar(64) DEFAULT NULL,
  `label` varchar(128) DEFAULT NULL,
  `location_x` float DEFAULT NULL,
  `location_y` float DEFAULT NULL,
  `media_id` varchar(1024) DEFAULT NULL,
  `pic_url` varchar(1024) DEFAULT NULL,
  `scale` varchar(64) DEFAULT NULL,
  `thumb_media_id` varchar(1024) DEFAULT NULL,
  `title` varchar(1024) DEFAULT NULL,
  `url` varchar(1024) DEFAULT NULL,
  `recognition` varchar(16) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE,
  KEY `FromUserName` (`from_user_name`) USING BTREE,
  KEY `ToUserName` (`to_user_name`) USING BTREE,
  KEY `MsgId` (`msg_id`(255)) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_news` (
  `id` bigint(11) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL,
  `ref_id` bigint(11) DEFAULT NULL COMMENT 'id of wx_media',
  `ref_idx` bigint(11) DEFAULT NULL,
  `type` varchar(16) CHARACTER SET utf8 DEFAULT NULL,
  `media_id` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `title` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `thumb_media_id` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `thumb_resource_id` bigint(20) DEFAULT NULL,
  `show_cover_pic` tinyint(4) DEFAULT NULL,
  `author` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `digest` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'type为news时表示图文的链接，type为linkNews时表示点击点击图文跳转的链接',
  `content_source_url` varchar(1024) CHARACTER SET utf8 DEFAULT NULL COMMENT 'type为news时表示点击图文的原文链接跳转的链接，type为linkNews时表示缩略图的微信链接',
  `update_time` int(11) DEFAULT NULL,
  `content` longtext,
  `description` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `name` varchar(256) CHARACTER SET utf8 DEFAULT NULL,
  `sort` int(11) DEFAULT NULL COMMENT '排序',
  `created_at` datetime DEFAULT NULL,
  `picture` varchar(1024) CHARACTER SET utf8 DEFAULT NULL,
  `created_ip` varchar(128) CHARACTER SET utf8 DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) CHARACTER SET utf8 DEFAULT NULL,
  `recommend` varchar(1024) DEFAULT NULL COMMENT '推荐语',
  `is_original` tinyint(4) DEFAULT NULL COMMENT '是否原创',
  `is_share` tinyint(4) DEFAULT NULL COMMENT '是否共享',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `acct_id` (`acct_id`,`ref_id`,`ref_idx`) USING BTREE,
  KEY `ref_id` (`ref_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_program_cfg` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `pubtype` tinyint(11) DEFAULT NULL COMMENT '公众号类型：0,未认证订阅号;1,未认证服务号;2,认证订阅号;3,认证服务号',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_push_details` (
  `id` bigint(20) NOT NULL COMMENT '图文群发统计详情',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `ref_date` date DEFAULT NULL COMMENT '推文日期',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `msgid` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图文消息id_索引(如：10000050_1)',
  `title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图文消息的标题',
  `user_source` int(11) DEFAULT NULL,
  `target_user` int(11) DEFAULT NULL COMMENT '送达人数',
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `int_page_from_session_read_user` int(11) DEFAULT NULL COMMENT '公众号会话阅读人数',
  `int_page_from_session_read_count` int(11) DEFAULT NULL COMMENT '公众号会话阅读次数',
  `int_page_from_hist_msg_read_user` int(11) DEFAULT NULL COMMENT '历史消息页阅读人数',
  `int_page_from_hist_msg_read_count` int(11) DEFAULT NULL COMMENT '历史消息页阅读次数',
  `int_page_from_feed_read_user` int(11) DEFAULT NULL COMMENT '朋友圈阅读人数',
  `int_page_from_feed_read_count` int(11) DEFAULT NULL COMMENT '朋友圈阅读次数',
  `int_page_from_friends_read_user` int(11) DEFAULT NULL COMMENT '好友转发阅读人数',
  `int_page_from_friends_read_count` int(11) DEFAULT NULL COMMENT '好友转发阅读次数',
  `int_page_from_other_read_user` int(11) DEFAULT NULL COMMENT '其他场景阅读人数',
  `int_page_from_other_read_count` int(11) DEFAULT NULL COMMENT '其他场景阅读次数',
  `feed_share_from_session_user` int(11) DEFAULT NULL COMMENT '公众号会话转发朋友圈人数',
  `feed_share_from_session_cnt` int(11) DEFAULT NULL COMMENT '公众号会话转发朋友圈次数',
  `feed_share_from_feed_user` int(11) DEFAULT NULL COMMENT '朋友圈转发朋友圈人数',
  `feed_share_from_feed_cnt` int(11) DEFAULT NULL COMMENT '朋友圈转发朋友圈次数',
  `feed_share_from_other_user` int(11) DEFAULT NULL COMMENT '其他场景转发朋友圈人数',
  `feed_share_from_other_cnt` int(11) DEFAULT NULL COMMENT '其他场景转发朋友圈次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_push_increment` (
  `id` bigint(20) NOT NULL COMMENT '图文群发每日统计数据(增量)',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `msgid` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图文消息id_索引(如：10000050_1)',
  `title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图文消息的标题',
  `user_source` int(11) DEFAULT NULL,
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_push_statistics` (
  `id` bigint(20) NOT NULL COMMENT '图文群发统计详情',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `ref_date` date DEFAULT NULL COMMENT '数据日期',
  `user_source` int(11) DEFAULT NULL COMMENT '0:会话;1.好友;2.朋友圈;3.腾讯微博;4.历史消息页;5.其他;6.看一看;7.搜一搜',
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_push_total` (
  `id` bigint(20) NOT NULL COMMENT '图文群发统计总数',
  `acct_id` bigint(20) DEFAULT NULL COMMENT '公众号id',
  `url` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'url',
  `ref_date` date DEFAULT NULL COMMENT '推文日期',
  `stat_date` date DEFAULT NULL COMMENT '统计日期',
  `msgid` varchar(64) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT '图文消息id_索引(如：10000050_1)',
  `title` varchar(255) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '图文消息的标题',
  `target_user` int(11) DEFAULT NULL COMMENT '送达人数',
  `user_source` int(11) DEFAULT NULL,
  `int_page_read_user` int(11) DEFAULT NULL COMMENT '图文页（点击群发图文卡片进入的页面）的阅读人数',
  `int_page_read_count` int(11) DEFAULT NULL COMMENT '图文页的阅读次数',
  `ori_page_read_user` int(11) DEFAULT NULL COMMENT '原文页（点击图文页“阅读原文”进入的页面）的阅读人数，无原文页时此处数据为0',
  `ori_page_read_count` int(11) DEFAULT NULL COMMENT '原文页的阅读次数',
  `share_user` int(11) DEFAULT NULL COMMENT '分享的人数',
  `share_count` int(11) DEFAULT NULL COMMENT '分享的次数',
  `add_to_fav_user` int(11) DEFAULT NULL COMMENT '收藏的人数',
  `add_to_fav_count` int(11) DEFAULT NULL COMMENT '收藏的次数',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(1024) COLLATE utf8_unicode_ci DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_qrcode` (
  `id` bigint(12) unsigned NOT NULL,
  `acct_id` bigint(11) DEFAULT NULL,
  `action_name` varchar(64) DEFAULT NULL COMMENT '二维码类型，QR_SCENE为临时的整型参数值，QR_STR_SCENE为临时的字符串参数值，QR_LIMIT_SCENE为永久的整型参数值，QR_LIMIT_STR_SCENE为永久的字符串参数值',
  `scene_id` int(12) DEFAULT NULL COMMENT '业务id',
  `scene_str` varchar(128) DEFAULT NULL COMMENT '业务字符串',
  `expire_seconds` int(12) DEFAULT NULL COMMENT '该二维码有效时间，以秒为单位。 最大不超过2592000（即30天），此字段如果不填，则默认有效期为30秒。',
  `ticket` varchar(512) DEFAULT NULL COMMENT '获取的二维码ticket，凭借此ticket可以在有效时间内换取二维码',
  `url` varchar(512) DEFAULT NULL,
  `qr_url` varchar(512) DEFAULT NULL COMMENT '二维码图片解析后的地址，开发者可根据该地址自行生成需要的二维码图片',
  `expired_type` varchar(16) DEFAULT NULL COMMENT '类型：perm永久，temp临时',
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `sence_id` (`scene_id`) USING BTREE,
  KEY `scene_str` (`scene_str`) USING BTREE,
  KEY `acct_id` (`acct_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_resource` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `acct_id` int(11) DEFAULT NULL,
  `type` varchar(16) DEFAULT NULL,
  `media_id` varchar(128) DEFAULT NULL,
  `title` varchar(256) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL,
  `url` varchar(1024) DEFAULT NULL,
  `update_time` int(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL,
  `create_at` int(11) DEFAULT NULL,
  `ref_id` int(11) DEFAULT NULL COMMENT 'id of wx_media',
  `ref_idx` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `media_id` (`media_id`) USING BTREE,
  KEY `acct_id` (`acct_id`,`ref_id`,`ref_idx`) USING BTREE,
  KEY `ref_id` (`ref_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=504 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_rule` (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `acct_id` bigint(11) DEFAULT NULL,
  `name` varchar(256) DEFAULT NULL COMMENT '规则名称',
  `rule_type` varchar(32) DEFAULT NULL COMMENT '规则类型  keyword,subscribe,default',
  `created_at` datetime DEFAULT NULL,
  `created_ip` varchar(16) DEFAULT NULL,
  `description` varchar(1024) DEFAULT NULL COMMENT '附件描述',
  `way` varchar(32) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(512) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=944465501773033473 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_rule_media` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `media_id` bigint(20) DEFAULT NULL COMMENT '媒体id',
  `rule_id` bigint(20) DEFAULT NULL COMMENT '规则id',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `rule_id` (`rule_id`) USING BTREE,
  KEY `media_id` (`media_id`,`rule_id`) USING BTREE,
  KEY `media_id_2` (`media_id`,`rule_id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=961469252887883777 DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_tags` (
  `id` bigint(100) unsigned NOT NULL,
  `acct_id` bigint(20) DEFAULT NULL,
  `tag_id` int(11) DEFAULT NULL,
  `name` varchar(64) DEFAULT NULL,
  `count` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `memo` varchar(128) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `tag_id` (`tag_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_user_tags` (
  `id` bigint(40) unsigned NOT NULL,
  `tags_id` bigint(40) DEFAULT NULL,
  `user_id` bigint(40) DEFAULT NULL,
  `openid` varchar(64) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `tag_id` (`tags_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_website_cfg` (
  `id` bigint(20) NOT NULL COMMENT '账户ID',
  `account_id` int(11) DEFAULT NULL COMMENT '账号',
  `acct_name` varchar(100) DEFAULT NULL COMMENT '账号名称',
  `original` varchar(45) DEFAULT NULL COMMENT '原始ID',
  `appid` varchar(50) DEFAULT NULL COMMENT 'appid',
  `secret` varchar(50) DEFAULT NULL COMMENT 'appsecret',
  `scope` varchar(20) DEFAULT NULL COMMENT 'scope',
  `encodingaeskey` varchar(255) DEFAULT NULL COMMENT '加密',
  `service_url` varchar(255) DEFAULT NULL COMMENT '服务器地址',
  `token` varchar(1024) DEFAULT NULL COMMENT 'token，对于微信来说，就是access_token\n',
  `token_expire` int(10) DEFAULT NULL COMMENT 'token失效时间',
  `ticket` varchar(1024) DEFAULT NULL COMMENT 'js的ticket',
  `ticket_expire` int(10) DEFAULT NULL COMMENT 'ticket的过期时间',
  `url_follow` varchar(250) DEFAULT NULL COMMENT '关注的引导链接',
  `url_oauth` varchar(250) DEFAULT NULL COMMENT '授权链接',
  `url_userinfo` varchar(250) DEFAULT NULL COMMENT '取用户信息基础接口地址',
  `url_callback` varchar(250) DEFAULT NULL COMMENT '回调地址',
  `url_token` varchar(250) DEFAULT NULL COMMENT 'access token',
  `url_oauth_token` varchar(250) DEFAULT NULL COMMENT '在oauth2.0中的access token',
  `create_time` datetime DEFAULT NULL COMMENT '增加的时间',
  `create_ip` varchar(128) DEFAULT NULL COMMENT '增加的ip',
  `real_appid` varchar(128) DEFAULT NULL COMMENT '真实appid',
  `token_msg` varchar(256) DEFAULT NULL COMMENT '消息接口的验证Token',
  `url_ticket` varchar(256) DEFAULT NULL,
  `url_auth` varchar(256) DEFAULT NULL,
  `url_refresh_token` varchar(256) DEFAULT NULL,
  `url_user_info` varchar(256) DEFAULT NULL,
  `active` tinyint(4) DEFAULT '1' COMMENT '是否默认',
  `wechat` varchar(255) DEFAULT NULL,
  `deleted_at` datetime DEFAULT NULL,
  `attend_code` varchar(255) DEFAULT NULL COMMENT '关注的二维码',
  `logo` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'logo图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC;

CREATE TABLE IF NOT EXISTS `wx_article` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `appid` varchar(40) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信appid（对应公众号）',
  `title` varchar(512) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '文章标题',
  `mid` varchar(32) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '微信文章url中的mid',
  `midx` tinyint(3) DEFAULT '1' COMMENT '文章索引',
  `thumb_url` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '封面url',
  `thumb_resource_id` bigint(20) DEFAULT NULL COMMENT '封面对应的文件id',
  `author` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '作者',
  `digest` varchar(512) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '摘要',
  `url` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '原文链接',
  `content_source_url` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '点击图文的原文链接跳转的链接',
  `content` longtext CHARACTER SET utf8mb4 COMMENT '原文html内容',
  `description` text CHARACTER SET utf8mb4 COMMENT '描述',
  `sort` int(11) DEFAULT NULL COMMENT '排序，越小越前',
  `created_at` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间',
  `created_ip` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL,
  `recommend` varchar(128) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '推荐语',
  `is_original` tinyint(3) DEFAULT NULL COMMENT '是否原创',
  `is_share` tinyint(3) DEFAULT NULL COMMENT '是否共享',
  `is_deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `create_time` datetime DEFAULT NULL COMMENT '文章创建/发布时间',
  `update_time` datetime DEFAULT NULL COMMENT '文章上次更新时间',
  `thumb_url_11` varchar(1024) CHARACTER SET utf8mb4 DEFAULT NULL COMMENT '1:1封面url',
  `state` tinyint(3) DEFAULT '1' COMMENT '文章状态1正常，0已删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_appid_mid` (`appid`,`mid`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信公众号上的文章';

CREATE TABLE IF NOT EXISTS `wx_article_comment` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间',
  `is_deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `msg_data_id` varchar(32) DEFAULT NULL COMMENT '冗余',
  `article_id` bigint(20) DEFAULT NULL COMMENT '文章id',
  `user_comment_id` int(11) NOT NULL COMMENT '用户评论id ',
  `openid` varchar(60) DEFAULT NULL COMMENT 'openid，用户如果用非微信身份评论，不返回openid',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `content` text COMMENT '评论内容 ',
  `comment_type` tinyint(3) DEFAULT NULL COMMENT '是否精选评论，0为即非精选，1为true，即精选',
  `author_reply_time` datetime DEFAULT NULL COMMENT '作者回复时间',
  `author_reply_content` text COMMENT '作者回复内容 ',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_main` (`article_id`,`msg_data_id`,`is_deleted`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文章留言表';

-- msg_data_id 保持 与wx_push_total 一致才能用到索引
CREATE TABLE IF NOT EXISTS `wx_article_msg_link` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `msg_data_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_unicode_ci NOT NULL COMMENT 'msg_data_id',
  `article_id` bigint(20) NOT NULL COMMENT 'wx_article_id',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_main` (`article_id`,`msg_data_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信文章和推送的msg_data_id关联';

CREATE TABLE IF NOT EXISTS `wx_article_sync_config` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '记录创建时间',
  `updated_at` datetime DEFAULT NULL COMMENT '记录更新时间',
  `is_deleted` tinyint(3) DEFAULT '0' COMMENT '是否删除',
  `appid` varchar(40) NOT NULL COMMENT '微信appid',
  `expect_date` date DEFAULT NULL COMMENT '期望同步到的日期',
  `last_sync_date` date DEFAULT NULL COMMENT '上次同步到的日期',
  `last_sync_max_date` date DEFAULT NULL COMMENT '上次同步到的最大日期',
  `enable` tinyint(3) DEFAULT NULL COMMENT '是否启用',
  `default_job_num` int(11) DEFAULT '0' COMMENT '默认同步数量（每日）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信文章同步配置表';

CREATE TABLE IF NOT EXISTS `competition_analysis_key_word` (
  `id` bigint(20) NOT NULL,
  `detecting_key_word` varchar(48) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '检测关键词',
  `correct_words` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合规正确词（多个词用 、分隔）',
  `created_at` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  `updated_at` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `deleted_at` datetime(0) NULL DEFAULT NULL COMMENT '删除时间',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `status` tinyint(4) NULL DEFAULT NULL COMMENT '状态',
  `meno` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '内容差错监控关键词库';

CREATE TABLE IF NOT EXISTS `uaa_operate_success_log` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `created_at` datetime DEFAULT NULL COMMENT '创建时间',
  `type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志类型',
  `sub_type` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志子类型',
  `user_id` bigint(20) DEFAULT NULL COMMENT '系统用户id',
  `action` varchar(511) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '日志内容',
  `biz_no` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务标识(被操作对象的业务编号)',
  `extra` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '额外',
  PRIMARY KEY (`id`),
  KEY `type_subtype_aid` (`type`,`sub_type`,`user_id`,`biz_no`) USING BTREE COMMENT '在什么类型下的子类型谁的操作'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Uaa操作成功日志（原则上该表仅记录成功的业务记录）';

CREATE TABLE IF NOT EXISTS `wx_user_phone_record` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `appid` varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '微信appid',
  `openid` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '微信openid',
  `phone` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `appid_openid_phone` (`openid`,`appid`,`phone`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='微信用户-登录手机记录';

CREATE TABLE IF NOT EXISTS `sys_menu_view_log` (
 `id` bigint(20) NOT NULL,
 `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
 `ref_type` tinyint(1) DEFAULT NULL COMMENT '菜单来源。1-移动端；2-管理端',
 `ref_id` bigint(20) DEFAULT NULL COMMENT '来源id',
 `ref_url` varchar(512) DEFAULT NULL COMMENT '来源页面url',
 `bk_name` varchar(64) DEFAULT NULL COMMENT '菜单名',
 `bk_url` varchar(512) DEFAULT NULL COMMENT '菜单跳转路由',
 `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否已删除。0-否；1-是',
 `created_at` datetime DEFAULT NULL COMMENT '创建时间',
 `updated_at` datetime DEFAULT NULL COMMENT '最后更新时间',
 PRIMARY KEY (`id`),
 KEY `user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单点击记录';

END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
