# 统计报表功能说明

## 概述

本文档描述了基于数据库表结构实现的统计报表功能模块，该模块提供了小散工程项目的核心统计指标。

## 功能特性

### 统计指标

1. **施工单位数量统计**
   - 数据来源：`contracting_unit` 表
   - 统计逻辑：统计未删除的施工单位总数
   - 过滤条件：`is_deleted = 0`

2. **工程总数统计**
   - 数据来源：`sporadic_project` 表
   - 统计逻辑：统计未删除的工程项目总数
   - 过滤条件：`is_deleted = 0`

3. **施工中工程数量统计**
   - 数据来源：`sporadic_project` 表
   - 统计逻辑：统计当前正在施工的工程项目数量
   - 过滤条件：`is_deleted = 0 AND status = 1`

4. **工程总金额统计**
   - 数据来源：`sporadic_project` 表的 `amount` 字段
   - 统计逻辑：计算所有未删除工程的金额总和
   - 单位转换：从元转换为万元（除以10000）
   - 精度：保留2位小数

## API接口

### 获取统计数据

**接口地址：** `POST /report/statistics`

**请求方式：** POST

**请求参数：** 无

**响应格式：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "contractingUnitCount": 25,
    "totalProjectCount": 150,
    "ongoingProjectCount": 45,
    "totalAmount": 1250.50
  }
}
```

**响应字段说明：**
- `contractingUnitCount`: 施工单位数量
- `totalProjectCount`: 工程总数
- `ongoingProjectCount`: 施工中工程数量
- `totalAmount`: 工程总金额（万元）

## 技术实现

### 架构设计

本功能模块遵循项目现有的分层架构：

```
Controller Layer (控制器层)
    ↓
Service Layer (服务层)
    ↓
Repository Layer (数据访问层)
    ↓
Database (数据库层)
```

### 核心组件

1. **IReportService** - 报表服务接口
   - 定义统计数据获取方法

2. **ReportServiceImpl** - 报表服务实现类
   - 实现具体的统计逻辑
   - 使用原生SQL查询提高性能

3. **ReportController** - 报表控制器
   - 提供RESTful API接口
   - 处理HTTP请求和响应

4. **StatisticsRespDTO** - 统计数据响应DTO
   - 封装统计结果数据

### 数据库查询

使用原生SQL查询确保高性能：

```sql
-- 施工单位数量
SELECT COUNT(1) FROM contracting_unit WHERE is_deleted = 0

-- 工程总数
SELECT COUNT(1) FROM sporadic_project WHERE is_deleted = 0

-- 施工中工程数量
SELECT COUNT(1) FROM sporadic_project WHERE is_deleted = 0 AND status = 1

-- 工程总金额
SELECT COALESCE(SUM(amount), 0) FROM sporadic_project WHERE is_deleted = 0
```

## 测试

### 单元测试

- **ReportServiceImplTest**: 测试服务层统计逻辑
- **ReportControllerTest**: 测试控制器层API接口

### 运行测试

```bash
# 运行所有测试
mvn test

# 运行特定测试类
mvn test -Dtest=ReportServiceImplTest
mvn test -Dtest=ReportControllerTest
```

## 使用示例

### 前端调用示例

```javascript
// 使用fetch API调用统计接口
fetch('/report/statistics', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    }
})
.then(response => response.json())
.then(data => {
    console.log('统计数据:', data.data);
    // 处理统计数据...
})
.catch(error => {
    console.error('获取统计数据失败:', error);
});
```

### 后端调用示例

```java
@Autowired
private IReportService reportService;

public void displayStatistics() {
    StatisticsRespDTO statistics = reportService.getStatistics();
    
    System.out.println("施工单位数量: " + statistics.getContractingUnitCount());
    System.out.println("工程总数: " + statistics.getTotalProjectCount());
    System.out.println("施工中工程数量: " + statistics.getOngoingProjectCount());
    System.out.println("工程总金额: " + statistics.getTotalAmount() + "万元");
}
```

## 注意事项

1. **性能考虑**：使用原生SQL查询以获得最佳性能
2. **数据一致性**：所有查询都基于 `is_deleted = 0` 条件过滤已删除数据
3. **金额精度**：金额计算保留2位小数，使用四舍五入规则
4. **异常处理**：服务层包含适当的日志记录和异常处理

## 扩展建议

1. **缓存优化**：可以考虑添加Redis缓存来提高查询性能
2. **权限控制**：根据用户权限过滤统计数据
3. **时间范围**：支持按时间范围进行统计
4. **更多维度**：增加按地区、类别等维度的统计
