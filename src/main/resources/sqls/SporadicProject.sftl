-- getProjectBySipUserIdAndChannel
SELECT
  sp.id,
  sp.region_pid as regionPid,
  sp.region_id as regionId,
  sp.region_cid as regionCid
FROM
  sporadic_project as sp
  JOIN event_camera_point as ecp ON ecp.project_id = sp.id
WHERE
  ecp.sip_user_id = :sipUserId
  AND ecp.channel = :channel
  AND ecp.is_deleted = 0
  AND sp.is_deleted = 0
  limit 1

-- updateMonitorFlagById
UPDATE sporadic_project
SET  monitor_flag = CASE
               WHEN EXISTS (SELECT 1 FROM event_camera_point WHERE project_id =  :projectId AND is_deleted = 0 ) THEN 1
               ELSE 2
END
WHERE id = :projectId


-- mobileIndex
SELECT
    p.id,
    p.name,
    p.status,
    p.poi_id poiId,
    p.monitor_flag monitorFlag,
    p.project_number  projectNumber,
    p.address,
    p.start_at startAt,
    p.end_at endAt,
    0  inspectRecordNum,
    0  violationRecordNum ,
    0 rectifyRecordNum,
    (SELECT COUNT(ID) FROM aiot_event WHERE project_id = p.id AND is_deleted = 0 )   eventNumber,
    0  rectifyEventNumber,
    o.id orderId,
    o.flow flow,
    o.flow_id flowId,
    o.sort sort
FROM sporadic_project p
LEFT JOIN monitor_order o ON p.id = o.project_id
WHERE p.is_deleted = 0
<#if organizationId??>
        AND p.organization_id = :organizationId
</#if>
<#if keyword??>
        AND (p.name LIKE CONCAT('%',:keyword,'%') or p.address LIKE CONCAT('%',:keyword,'%'))
</#if>
<#if projectIdSet??>
        AND p.id in (:projectIdSet)
</#if>
<#if status??>
        AND p.status = :status
</#if>
<#if monitorFlag??>
        AND p.monitor_flag = :monitorFlag
</#if>
<#if flow??>
        AND o.flow = :flow
</#if>
<#if flowList??>
        AND o.flow IN (:flowList)
</#if>
<#if flowId??>
        AND o.flow_id = :flowId
</#if>
<#if regionIdSet??>
        AND p.region_id in (:regionIdSet)
<#elseif regionCidSet??>
        AND p.region_cid in (:regionCidSet)
<#elseif regionPidSet??>
        AND p.region_pid in (:regionPidSet)
</#if>

<#if regionPid?? && regionPid?length gt 0>
        and p.region_pid = :regionPid
</#if>
<#if regionId?? && regionId?length gt 0>
        and p.region_id = :regionId
</#if>
<#if regionCid?? && regionCid?length gt 0>
        and p.region_cid = :regionCid
</#if>
ORDER BY o.sort ASC , o.id DESC

-- queryProjects
SELECT p.* FROM sporadic_project p
WHERE p.is_deleted = 0
<#if organizationId??>
        AND p.organization_id = :organizationId
</#if>
<#if keyword??>
        AND (p.name LIKE CONCAT('%',:keyword,'%') or p.address LIKE CONCAT('%',:keyword,'%'))
</#if>
<#if projectId??>
        AND p.id in (:projectId)
<#elseif projectIdSet??>
        AND p.id in (:projectIdSet)
</#if>
<#if status??>
        AND p.status = :status
</#if>
<#if regionCidSet??>
        AND p.region_cid in (:regionCidSet)
<#elseif regionIdSet??>
        AND p.region_id in (:regionIdSet)
<#elseif regionPidSet??>
        AND p.region_pid in (:regionPidSet)
</#if>
<#if regionPid?? && regionPid?length gt 0>
        and p.region_pid = :regionPid
</#if>
<#if regionId?? && regionId?length gt 0>
        and p.region_id = :regionId
</#if>
<#if regionCid?? && regionCid?length gt 0>
        and p.region_cid = :regionCid
</#if>
ORDER BY p.id DESC