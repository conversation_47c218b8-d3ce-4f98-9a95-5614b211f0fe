server:
  port: 12245
  max-http-header-size: 65536
nacos:
  host: ${NACOS_HOST:*************:8848}
  namespace: ${NACOS_NAMESPACE:local}
  group: ${NACOS_GROUP:scloud-group}
  username: ${NACOS_USERNAME:nacos}
  password: ${NACOS_PASSWORD:nacos}

spring:
  application:
    name: @pom.artifactId@
  servlet:
    multipart:
      max-file-size: 10MB
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      username: ${nacos.username}
      password: ${nacos.password}
      server-addr: ${nacos.host}
      discovery:
        namespace: ${nacos.namespace}
        server-addr: ${nacos.host}
        metadata:
          group: ${nacos.group}
          version: 1.0
      config:
        namespace: ${nacos.namespace}
        server-addr: ${nacos.host}
        shared-configs:
          - data-id: application-jpa.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-log.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-management.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-mysql.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-rabbitmq.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-xsl-redis.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-ribbon.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-sentinel.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-klock.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-es.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-network.yml
            group: ${nacos.group}
            refresh: false
          - data-id: application-server.yml
            group: ${nacos.group}
            refresh: true
          - data-id: application-kafka.yml
            group: ${nacos.group}
            refresh: false
          - data-id: file-dynamic.yml
            group: ${nacos.group}
            refresh: true
          - data-id: ${spring.application.name}.yml
            group: ${nacos.group}
            refresh: true