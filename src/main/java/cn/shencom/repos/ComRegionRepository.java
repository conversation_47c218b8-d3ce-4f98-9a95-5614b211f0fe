package cn.shencom.repos;

import cn.shencom.model.ComRegion;
import cn.shencom.model.dto.query.ComRegionQueryDTO;
import cn.shencom.model.dto.resp.ComRegionSimpleDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;


/**
 * 区域表 的Repository
 *
 * <AUTHOR>
 * @since 2025-04-19
 */
@Repository
public interface ComRegionRepository extends JpaRepository<ComRegion, String>, JpaSpecificationExecutor<ComRegion> {

    @Query(nativeQuery = true, value = "SELECT id FROM com_region WHERE deleted_at IS NULL AND p_id = ?1 AND title = ?2")
    String findIdByPidAndTitle(String pid, String title);

    @Query(nativeQuery = true, value = "SELECT title FROM `com_region` where deleted_at IS NULL AND id = ?1 ")
    String getTitleById(String id);

    @TemplateQuery
    List<ComRegionSimpleDTO> findSimpleRegions(ComRegionQueryDTO bean);

}
