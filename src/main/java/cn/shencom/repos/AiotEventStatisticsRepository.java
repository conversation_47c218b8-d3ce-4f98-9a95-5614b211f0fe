package cn.shencom.repos;

import cn.shencom.model.AiotEventStatistics;

import java.util.Date;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * 事件统计分类配置表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Repository
public interface AiotEventStatisticsRepository
                extends JpaRepository<AiotEventStatistics, String>, JpaSpecificationExecutor<AiotEventStatistics> {

        /**
         * 根据日期范围查询事件统计，支持 null 值
         * 
         * @param startDate 开始日期
         * @param endDate   结束日期
         * @param projectId 项目id
         * @return 事件统计列表
         */
        @Query("SELECT a FROM AiotEventStatistics a WHERE a.isDeleted = 0 " +
                        "AND a.eventDate BETWEEN :startDate AND :endDate " +
                        "AND a.projectId IN :projectId")
        List<AiotEventStatistics> queryByEventDateBetweenAndProjectIdIn(@Param("startDate") Date startDate,
                        @Param("endDate") Date endDate, @Param("projectId") List<String> projectId);

}
