package cn.shencom.repos;

import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import com.slyak.spring.jpa.TemplateQuery;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 小散工程表 的Repository
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Repository
public interface SporadicProjectRepository
                extends JpaRepository<SporadicProject, String>, JpaSpecificationExecutor<SporadicProject> {
        SporadicProject findFirstByRegionPidAndRegionIdAndRegionCidAndName(String regionPid, String regionId,
                        String regionCid, String name);

        @TemplateQuery
        SporadicProjectRespDTO getProjectBySipUserIdAndChannel(@Param("sipUserId") String sipUserId,
                        @Param("channel") String channel);

        @TemplateQuery
        @Modifying
        @Transactional
        int updateMonitorFlagById(@Param("projectId") String projectId);

        @TemplateQuery
        List<SporadicProjectRespDTO> queryProjects(SporadicProjectMobileQueryDTO bean);

        @TemplateQuery
        Page<SporadicProjectRespDTO> mobileIndex(SporadicProjectMobileQueryDTO bean, Pageable page);

        @TemplateQuery
        List<SporadicProjectRespDTO> mobileIndex(SporadicProjectMobileQueryDTO bean);

        /**
         * 查询需要结束施工的项目
         */
        List<SporadicProject> findByEndAtLessThanAndStatus(Date date, Integer status);

        /**
         * 查询需要开始施工的项目
         */
        List<SporadicProject> findByStartAtLessThanAndStatus(Date date, Integer status);

        /**
         * 通过组织id查询项目
         */
        List<SporadicProject> findByOrganizationId(String organizationId);

        /**
         * 通过施工单位id查询项目
         */
        List<SporadicProject> findByContractorIdIn(List<String> contractorIds);
}
