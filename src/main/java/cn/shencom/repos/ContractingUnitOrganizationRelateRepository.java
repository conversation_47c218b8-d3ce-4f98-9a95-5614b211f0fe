package cn.shencom.repos;

import cn.shencom.model.ContractingUnitOrganizationRelate;

import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

/**
 * 施工单位组织关联表 的Repository
 *
 * <AUTHOR>
 * @since 2025-07-16
 */
@Repository
public interface ContractingUnitOrganizationRelateRepository
    extends JpaRepository<ContractingUnitOrganizationRelate, String>,
    JpaSpecificationExecutor<ContractingUnitOrganizationRelate> {

    Optional<ContractingUnitOrganizationRelate> findByContractingUnitIdAndOrganizationId(String contractingUnitId,
            String organizationId);
}
