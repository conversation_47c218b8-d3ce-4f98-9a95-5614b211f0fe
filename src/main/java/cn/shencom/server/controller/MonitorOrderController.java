package cn.shencom.server.controller;

import cn.shencom.model.dto.create.MonitorOrderCreateDTO;
import cn.shencom.model.dto.query.MonitorOrderQueryDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespDTO;
import cn.shencom.model.dto.update.MonitorOrderUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IMonitorOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * 小散工程-监管工单 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@RestController
@RequestMapping("/monitor/order")
public class MonitorOrderController extends BaseController {

    @Autowired
    private IMonitorOrderService iMonitorOrderService;

    /**
     * 查询小散工程-监管工单列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link MonitorOrderRespDTO}>>
     */
    @PostMapping(value = {"/index"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Page<MonitorOrderRespDTO>> index(@RequestBody @Validated MonitorOrderQueryDTO bean) {
        return success(iMonitorOrderService.query(bean));
    }

    /**
     * 根据id查询小散工程-监管工单
     *
     * @param bean 条件
     * @return {@link Result<MonitorOrderRespDTO>}
     */
    @PostMapping(value = {"/show"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<MonitorOrderRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iMonitorOrderService.show(bean));
    }

    /**
     * 新建小散工程-监管工单
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/create"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> create(@RequestBody @Validated MonitorOrderCreateDTO bean) {
        return success(Objects.nonNull(iMonitorOrderService.create(bean)));
    }

    /**
     * 修改小散工程-监管工单
     *
     * @param bean 条件
     * @return {@link Result<Boolean>}
     */
    @PostMapping(value = {"/update"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result<Boolean> update(@RequestBody @Validated MonitorOrderUpdateDTO bean) {
        return success(Objects.nonNull(iMonitorOrderService.update(bean)));
    }

    /**
     * 删除小散工程-监管工单
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/delete"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result delete(@RequestBody @Validated ScDeleteDTO bean) {
        iMonitorOrderService.delete(bean.getIds());
        return success();
    }

    /**
     * 导出小散工程-监管工单
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = {"/export"}, produces = {MediaType.APPLICATION_JSON_UTF8_VALUE})
    public Result export(@RequestBody @Validated MonitorOrderQueryDTO bean) {
        iMonitorOrderService.export(bean);
        return success();
    }

}
