package cn.shencom.server.controller;

import cn.shencom.model.dto.resp.StatisticsRespDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 统计报表控制器
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@RestController
@RequestMapping("/report")
public class ReportController extends BaseController {

    @Autowired
    private IReportService reportService;

    /**
     * 获取统计数据
     * 包括：施工单位数量、工程总数、施工中工程数量、工程总金额
     *
     * @return {@link Result}<{@link StatisticsRespDTO}>
     */
    @PostMapping(value = {"/statistics"}, produces = {MediaType.APPLICATION_JSON_VALUE})
    public Result<StatisticsRespDTO> statistics() {
        return success(reportService.getStatistics());
    }
}
