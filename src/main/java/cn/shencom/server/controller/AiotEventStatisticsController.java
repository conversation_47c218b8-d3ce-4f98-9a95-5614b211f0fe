package cn.shencom.server.controller;

import cn.shencom.model.dto.create.AiotEventStatisticsCreateDTO;
import cn.shencom.model.dto.query.AiotEventStatisticsQueryDTO;
import cn.shencom.model.dto.resp.AiotEventStatisticsRespDTO;
import cn.shencom.model.dto.update.AiotEventStatisticsUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScDeleteDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.server.controller.BaseController;
import cn.shencom.server.service.IAiotEventStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * 事件统计分类配置表 的接口服务
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@RestController
@RequestMapping("/aiot/event/statistics")
public class AiotEventStatisticsController extends BaseController {

    @Autowired
    private IAiotEventStatisticsService iAiotEventStatisticsService;

    /**
     * 查询事件统计分类配置表列表
     *
     * @param bean 条件
     * @return {@link Result}<{@link Page}<{@link AiotEventStatisticsRespDTO}>>
     */
    @PostMapping(value = { "/index" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<Page<AiotEventStatisticsRespDTO>> index(@RequestBody @Validated AiotEventStatisticsQueryDTO bean) {
        return success(iAiotEventStatisticsService.query(bean));
    }

    /**
     * 根据id查询事件统计分类配置表
     *
     * @param bean 条件
     * @return {@link Result<AiotEventStatisticsRespDTO>}
     */
    @PostMapping(value = { "/show" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<AiotEventStatisticsRespDTO> show(@RequestBody @Validated ScShowDTO bean) {
        return success(iAiotEventStatisticsService.show(bean));
    }


    /**
     * 导出事件统计分类配置表
     *
     * @param bean 条件
     * @return {@link Result}
     */
    @PostMapping(value = { "/export" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result export(@RequestBody @Validated AiotEventStatisticsQueryDTO bean) {
        iAiotEventStatisticsService.export(bean);
        return success();
    }

    /**
     * 统计事件数量
     *
     * @param bean 统计DTO
     * @return 统计结果
     */
    @PostMapping(value = { "/count" }, produces = { MediaType.APPLICATION_JSON_UTF8_VALUE })
    public Result<List<AiotEventStatisticsRespDTO>> count(@RequestBody @Validated AiotEventStatisticsQueryDTO bean) {
        return success(iAiotEventStatisticsService.count(bean));
    }
}
