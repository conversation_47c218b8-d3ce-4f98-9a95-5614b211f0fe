package cn.shencom.server.service;

import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.utils.UserUtil.UtilsRegion;

import org.springframework.data.domain.Page;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 小散工程表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
public interface ISporadicProjectService {

    /**
     * 查询小散工程表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SporadicProjectRespDTO> query(SporadicProjectQueryDTO bean);


    /**
     * 无权限校验，根据id查询
     * @param bean
     * @return
     */
    Page<SporadicProjectRespDTO> queryByIds(SporadicProjectQueryDTO bean);


    /**
     * 根据id查询小散工程表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    SporadicProjectRespDTO show(ScShowDTO bean);

    /**
     * 新建小散工程表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    SporadicProject create(SporadicProjectCreateDTO bean);

    /**
     * 修改小散工程表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    SporadicProject update(SporadicProjectUpdateDTO bean);

    /**
     * 更新施工状态
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    void updateStatus(SporadicProjectUpdateDTO bean);

    /**
     * 删除小散工程表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出小散工程表
     *
     * @param bean 导出DTO
     */
    void export(SporadicProjectQueryDTO bean);

    Result<?> importExcel(MultipartFile file);

    /**
     * 更新监管状态
     */
    void updateMonitorFlag(String projectId);

    /**
     * 查询小散工程表列表- 移动端
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<SporadicProjectRespDTO> mobileIndex(SporadicProjectMobileQueryDTO bean);

    void autoUpdateStatus();

    /**
     * 查询小散工程表列表- 移动端
     *
     * @param bean 查询DTO
     * @return
     */
    List<SporadicProjectRespDTO> mobileSelect();

    void autoUpdateMonitorFlag();

    /**
     * 获取用户对应的项目id
     */
    List<SporadicProjectRespDTO> getUserProjectList(UtilsRegion region, SporadicProjectQueryDTO dto);

    /**
     * 获取用户对应的项目id
     */
    List<SporadicProjectRespDTO> getUserProjectList(SporadicProjectQueryDTO dto);

    /**
     * 获取用户对应的项目id
     */
    List<SporadicProjectRespDTO> getUserProjectList();

}
