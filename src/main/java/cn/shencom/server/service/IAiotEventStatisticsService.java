package cn.shencom.server.service;

import cn.shencom.model.AiotEventStatistics;
import cn.shencom.model.dto.create.AiotEventStatisticsCreateDTO;
import cn.shencom.model.dto.query.AiotEventStatisticsQueryDTO;
import cn.shencom.model.dto.resp.AiotEventStatisticsRespDTO;
import cn.shencom.model.dto.update.AiotEventStatisticsUpdateDTO;
import cn.shencom.scloud.common.dto.ScShowDTO;
import org.springframework.data.domain.Page;

import java.util.Date;
import java.util.List;

/**
 * 事件统计分类配置表 的服务接口
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
public interface IAiotEventStatisticsService {

    /**
     * 查询事件统计分类配置表列表
     *
     * @param bean 查询DTO
     * @return 分页列表
     */
    Page<AiotEventStatisticsRespDTO> query(AiotEventStatisticsQueryDTO bean);

    /**
     * 根据id查询事件统计分类配置表
     *
     * @param bean 查询DTO
     * @return 单个实体
     */
    AiotEventStatisticsRespDTO show(ScShowDTO bean);

    /**
     * 新建事件统计分类配置表
     *
     * @param bean 新建DTO
     * @return 创建实体
     */
    AiotEventStatistics create(AiotEventStatisticsCreateDTO bean);

    /**
     * 修改事件统计分类配置表
     *
     * @param bean 修改DTO
     * @return 修改实体
     */
    AiotEventStatistics update(AiotEventStatisticsUpdateDTO bean);

    /**
     * 删除事件统计分类配置表
     *
     * @param ids id集合
     */
    void delete(List<String> ids);

    /**
     * 导出事件统计分类配置表
     *
     * @param bean 导出DTO
     */
    void export(AiotEventStatisticsQueryDTO bean);

    /**
     * 统计事件数量
     *
     * @param bean 统计DTO
     * @return 统计结果
     */
    List<AiotEventStatisticsRespDTO> count(AiotEventStatisticsQueryDTO bean);

    /**
     * 更新事件统计
     */
    void updateEventStatistics(String sceneCode, Date eventDate, List<String> projectIds);
}
