package cn.shencom.server.service.impl;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeBuilder;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.shencom.constant.CommonConstant;
import cn.shencom.model.dto.query.ComRegionQueryDTO;
import cn.shencom.model.dto.resp.ComRegionSimpleDTO;
import cn.shencom.repos.ComRegionRepository;
import cn.shencom.scloud.common.base.constant.NumberConstant;
import cn.shencom.scloud.common.util.ScidContext;
import cn.shencom.server.service.IComRegionService;
import com.alibaba.fastjson.JSONArray;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 查询基础数据接口实现类
 *
 * <AUTHOR>
 * @date 2022/6/16 14:23
 */
@Service
@Slf4j
public class ComRegionServiceImpl implements IComRegionService {


    @Resource
    private ComRegionRepository comRegionRepository;


    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private Environment environment;




    @Override
    public List<Tree<String>> comRegionTree(ComRegionQueryDTO bean) {
        List<ComRegionSimpleDTO> regions = getRegions(bean);
        return buildTree(bean, regions);
    }

    private List<ComRegionSimpleDTO> getRegions(ComRegionQueryDTO bean) {
        String key = String.format("%s:%s:%s", ScidContext.getScid(), environment.getProperty("spring.application.name"), CommonConstant.REGION_SIMPLE_REDIS_KEY);
        String value = stringRedisTemplate.opsForValue().get(key);
        List<ComRegionSimpleDTO> regions;
        if (value != null) {
            regions = JSONArray.parseArray(value, ComRegionSimpleDTO.class);
        } else {
            regions = comRegionRepository.findSimpleRegions(bean);
            stringRedisTemplate.opsForValue().set(key, JSONArray.toJSONString(regions));
        }
        return regions;
    }

    private List<Tree<String>> buildTree(ComRegionQueryDTO bean, List<ComRegionSimpleDTO> regions) {
        // 获取根节点id，默认为深圳市
        String rootId = Optional.ofNullable(bean.getRootId()).orElse(CommonConstant.REGION_SHENZHEN);
        TreeBuilder<String> builder = TreeBuilder.of(rootId, new TreeNodeConfig()
                .setParentIdKey("pId")
                .setNameKey("title")
                .setDeep(bean.getDeep()));
        Tree<String> tree = builder.append(regions, (comRegion, treeNode) -> {
            treeNode.setId(comRegion.getId());
            treeNode.setName(comRegion.getTitle());
            treeNode.setParentId(comRegion.getPId());
            if (comRegion.getSort() != null) {
                treeNode.setWeight(-comRegion.getSort());
            }
            if (rootId.equals(comRegion.getId())) {
                builder.setName(comRegion.getTitle());
                builder.setParentId(comRegion.getPId());
            }
        }).build();
        // 默认不返回携带根节点、为1则携带
        if (NumberConstant.ONE_INT.equals(bean.getRoot())) {
            return Collections.singletonList(tree);
        } else {
            return tree.getChildren();
        }
    }

    @Override
    public void clearRegionRedis() {
        String key = String.format("%s:%s:%s", ScidContext.getScid(), environment.getProperty("spring.application.name"), CommonConstant.REGION_SIMPLE_REDIS_KEY);
        //先删除键
        stringRedisTemplate.delete(key);
    }

}
