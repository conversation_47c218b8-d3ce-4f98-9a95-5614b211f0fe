package cn.shencom.server.service.impl;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.hutool.core.collection.CollectionUtil;
import cn.shencom.constant.CommonConstant;
import cn.shencom.enums.BusinessMemberTypeEnum;
import cn.shencom.enums.EngineeringRoleEnum;
import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.create.MonitorFlowCreateDTO;
import cn.shencom.model.dto.create.SporadicProjectCreateDTO;
import cn.shencom.model.dto.excel.SporadicProjectExcelDTO;
import cn.shencom.model.dto.query.SporadicProjectMobileQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.ContractingUnitOrganizationRelateUpdateDTO;
import cn.shencom.model.dto.update.ContractingUnitUpdateDTO;
import cn.shencom.model.dto.update.EngineeringMembersUpdateDTO;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.model.dto.update.SporadicProjectUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.Result;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.base.util.BizAssert;
import cn.shencom.scloud.common.core.utils.UploadUtilV2;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.DateUtil;
import cn.shencom.scloud.common.util.GaoDeMapUtil;
import cn.shencom.scloud.common.util.export.ExportWordUtil2007;
import cn.shencom.scloud.common.util.export.ScExcelExportUtil;
import cn.shencom.scloud.scloudapifile.dto.ByteArrayMultipartFile;
import cn.shencom.scloud.scloudapifile.dto.FileUploadResourceDTO;
import cn.shencom.scloud.scloudapifile.service.FileService;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.*;
import cn.shencom.utils.UserUtil;
import cn.shencom.utils.UserUtil.UserUtilsProjectQueryDTO;
import cn.shencom.utils.UserUtil.UtilsRegion;
import cn.shencom.utils.XsgcContext;
import com.xxl.job.core.context.XxlJobHelper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 小散工程表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-29
 */
@Service
@Slf4j
public class SporadicProjectServiceImpl extends BaseImpl implements ISporadicProjectService {

    @Autowired
    private SporadicProjectRepository sporadicProjectRepository;

    @Resource
    private UploadUtilV2 uploadUtilV2;

    @Resource
    private Validator validator;

    @Resource
    private SporadicProjectCategoryRepository sporadicProjectCategoryRepository;

    @Resource
    private ComRegionRepository comRegionRepository;

    @Resource
    private FileService fileService;

    @Resource
    private GaoDeMapUtil gaoDeMapUtil;

    @Autowired
    private GisPoiRepository gisPoiRepository;

    @Autowired
    private IEngineeringMembersService engineeringMembersService;

    @Autowired
    private IMonitorOrderService monitorOrderService;

    @Autowired
    private IMonitorFlowService monitorFlowService;

    @Autowired
    private XsgcBusinessMembersRepository xsgcBusinessMembersRepository;

    @Autowired
    private EventCameraPointDeviceRepository eventCameraPointDeviceRepository;

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private IFnRmsv3MembersTypeRelateService fnRmsv3MembersTypeRelateService;

    @Autowired
    private EngineeringMembersRepository engineeringMembersRepository;

    @Autowired
    private EngineeringMembersProjectRelateRepository engineeringMembersProjectRelateRepository;

    @Autowired
    private IContractingUnitService contractingUnitService;

    @Autowired
    private IContractingUnitOrganizationRelateService contractingUnitOrganizationRelateService;

    @Autowired
    private UserUtil userUtil;

    @Autowired
    private ISporadicProjectMemoService sporadicProjectMemoService;

    @Override
    public Page<SporadicProjectRespDTO> query(SporadicProjectQueryDTO bean) {

        String catePid = ScQueryUtil.getValueAndRm(bean.getQuery(), "catePid");
        String cateId = ScQueryUtil.getValueAndRm(bean.getQuery(), "cateId");

        List<String> ids = new ArrayList<>();

        // 组织id
        String organizationId = XsgcContext.getOrganizationId();
        String userId = ScContext.getCurrentUserThrow().getId();

        // 如果组织id不为空，查询组织成员的区域信息
        FnRmsv3MembersTypeRelateRespDTO typeRelateRespDTO = null;
        if (organizationId != null) {
            typeRelateRespDTO = fnRmsv3MembersTypeRelateService.getMemberRelateByUserAndOrganization(userId,
                    organizationId);
            if (typeRelateRespDTO == null || typeRelateRespDTO.getRegionIds().isEmpty()) {
                return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
            }
        } else {

            //查询当前用户是否是业务人员
            // 判断当前是否是业务人员，如果是则不受限制
            XsgcBusinessMembers xsgcBusinessMembers = xsgcBusinessMembersRepository
                    .findFirstByUserIdAndStatus(userId, 1);

            if (xsgcBusinessMembers == null) {
                // 如果不是业务人员，则查询当前用户是否是施工人员
                EngineeringMembers engineeringMembers = engineeringMembersRepository.findFirstByUserId(userId);
                if (engineeringMembers != null) {
                    // 如果是施工人员，查询这个施工人员关联的项目
                    List<EngineeringMembersProjectRelate> projectRelateList = engineeringMembersProjectRelateRepository
                            .findByMemberId(engineeringMembers.getId());
                    if (projectRelateList.isEmpty()) {
                        return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
                    }
                    ids = projectRelateList.stream().map(EngineeringMembersProjectRelate::getProjectId)
                            .collect(Collectors.toList());
                }
            }
        }

        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProject.class);
        FnRmsv3MembersTypeRelateRespDTO finalTypeRelateRespDTO = typeRelateRespDTO;
        List<String> finalIds = ids;
        Page<SporadicProject> res = sporadicProjectRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            // 根据组织id进行筛选
            if (StringUtil.isNotEmpty(organizationId)) {
                ps.add(builder.equal(root.get("organizationId"), organizationId));
            }

            if (finalTypeRelateRespDTO != null) {
                // 根据区域进行筛选
                Integer level = finalTypeRelateRespDTO.getLevel();
                List<FnRmsv3MembersTypeRelateBinding> regionIds = finalTypeRelateRespDTO.getRegionIds();
                // 根据区域筛选
                if (!regionIds.isEmpty() && level != null) {

                    if (level == 1) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionPid)
                                .collect(Collectors.toList());
                        // 根据regionPid进行筛选
                        ps.add(builder.in(root.get("regionPid")).value(regionIdValues));
                    } else if (level == 2) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionId)
                                .collect(Collectors.toList());
                        // 根据regionId进行筛选
                        ps.add(builder.in(root.get("regionId")).value(regionIdValues));
                    } else if (level == 3) {
                        List<String> regionIdValues = regionIds.stream()
                                .map(FnRmsv3MembersTypeRelateBinding::getRegionCid)
                                .collect(Collectors.toList());
                        // 根据regionCid进行筛选
                        ps.add(builder.in(root.get("regionCid")).value(regionIdValues));
                    }
                }
            }

            if (!CollectionUtil.isEmpty(finalIds)) {
                ps.add(builder.in(root.get("id")).value(finalIds));
            }

            if (StringUtils.isNotEmpty(catePid)) {
                Join<Object, Object> pCateJoin = root.join("pCate", JoinType.LEFT);
                ps.add(builder.equal(pCateJoin.get("id"), catePid));
            }
            if (StringUtils.isNotEmpty(cateId)) {
                Join<Object, Object> pCateJoin = root.join("cate", JoinType.LEFT);
                ps.add(builder.equal(pCateJoin.get("id"), cateId));
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SporadicProjectRespDTO.class,
                (entity, dto) -> handleSporadicProjectDto(dto));
    }


    @Override
    public Page<SporadicProjectRespDTO> queryByIds(SporadicProjectQueryDTO bean) {
        List<String> ids = bean.getIds();
        if (bean.getIds().isEmpty()){
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProject.class);
        Page<SporadicProject> res = sporadicProjectRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (!CollectionUtil.isEmpty(ids)) {
                ps.add(builder.in(root.get("id")).value(ids));
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, SporadicProjectRespDTO.class,
                (entity, dto) -> handleSporadicProjectDto(dto));
    }

    @Override
    public SporadicProjectRespDTO show(ScShowDTO bean) {
        Optional<SporadicProject> option = sporadicProjectRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        SporadicProject entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(SporadicProject.class);
        return ScQueryUtil.handleOne(entity, linkMap, SporadicProjectRespDTO.class,
                (r, dto) -> handleSporadicProjectDto(dto));
    }

    protected void handleSporadicProjectDto(SporadicProjectRespDTO dto) {
        if (dto.getStatus() == 0) {
            dto.setStatusName("未开始");
        } else if (dto.getStatus() == 2) {
            dto.setStatusName("已结束");
        } else {
            dto.setStatusName("施工中");
        }

        // 查询关联的工单
        MonitorOrder monitorOrder = monitorOrderRepository.findByProjectId(dto.getId());
        if (monitorOrder != null) {
            dto.setFlow(monitorOrder.getFlow());
            dto.setFlowId(monitorOrder.getFlowId());
            dto.setOrderId(monitorOrder.getId());
        }
    }

    private int getProjectStatus(Date startAt, Date endAt) {
        Date start = DateUtil.getStartOfToday(LocalDate.now());
        Date end = DateUtil.getEndOfToday(LocalDate.now());
        if (end.compareTo(startAt) < 0) {
            return CommonConstant.PROJECT_STATUS_WAIT;
        } else if (start.compareTo(endAt) > 0) {
            return CommonConstant.PROJECT_STATUS_END;
        } else {
            return CommonConstant.PROJECT_STATUS_GOING;
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProject create(SporadicProjectCreateDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        String userId = ScContext.getCurrentUserThrow().getId();

        SporadicProject entity = new SporadicProject();
        BeanUtil.copyProperties(bean, entity);
        entity.setCreateUser(userId);
        // 更新经纬度
        GisPoi gisPoi = gisPoiRepository.findById(bean.getPoiId()).orElseThrow(() -> new ScException("点位不存在！"));
        entity.setLat(new BigDecimal(gisPoi.getLat()));
        entity.setLng(new BigDecimal(gisPoi.getLng()));
        entity.setOrganizationId(organizationId);

        // 检查当前组织人员所属区域
        if (!checkRegion(userId, organizationId, entity)) {
            throw new ScException("您无当前区域权限！");
        }

        // 获取施工状态
        entity.setStatus(getProjectStatus(entity.getStartAt(), entity.getEndAt()));

        SporadicProject save = sporadicProjectRepository.save(entity);
        // 创建或者更新工程成员
        createOrUpdateEngineer(save);
        // 创建监管流程工单
        monitorOrderService.createOrder(save);

        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SporadicProject update(SporadicProjectUpdateDTO bean) {

        // 组织id
        String organizationId = XsgcContext.getOrganizationId();
        String userId = ScContext.getCurrentUserThrow().getId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        SporadicProject entity = sporadicProjectRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);

        // 检查当前组织人员所属区域
        if (!checkRegion(userId, organizationId, entity)) {
            throw new ScException("您无当前区域权限！");
        }

        // 更新施工状态
        entity.setStatus(getProjectStatus(entity.getStartAt(), entity.getEndAt()));

        // 更新经纬度
        GisPoi gisPoi = gisPoiRepository.findById(bean.getPoiId()).orElseThrow(() -> new ScException("点位不存在！"));
        entity.setLat(new BigDecimal(gisPoi.getLat()));
        entity.setLng(new BigDecimal(gisPoi.getLng()));

        // 创建或者更新工程成员
        createOrUpdateEngineer(entity);

        return sporadicProjectRepository.save(entity);
    }

    /**
     * 检查当前组织人员所属区域
     * 
     * @param userId
     * @param organizationId
     * @param project
     */
    private boolean checkRegion(String userId, String organizationId, SporadicProject project) {
        FnRmsv3MembersTypeRelateRespDTO typeRelateRespDTO = fnRmsv3MembersTypeRelateService
                .getMemberRelateByUserAndOrganization(userId, organizationId);
        if (typeRelateRespDTO == null || typeRelateRespDTO.getRegionIds().isEmpty()
                || typeRelateRespDTO.getLevel() == null) {
            throw new ScException("当前组织用户无所属区域！");
        }
        String regionPid = project.getRegionPid();
        String regionId = project.getRegionId();
        String regionCid = project.getRegionCid();
        int level = typeRelateRespDTO.getLevel();
        List<FnRmsv3MembersTypeRelateBinding> regionIds = typeRelateRespDTO.getRegionIds();
        if (level == 1) {
            if (!regionIds.stream().anyMatch(region -> regionPid.equals(region.getRegionPid()))) {
                return false;
            }
        } else if (level == 2) {
            if (!regionIds.stream().anyMatch(region -> regionId.equals(region.getRegionId()))) {
                return false;
            }
        } else if (level == 3) {
            if (!regionIds.stream().anyMatch(region -> regionCid.equals(region.getRegionCid()))) {
                return false;
            }
        }
        return true;
    }

    @Override
    public void updateStatus(SporadicProjectUpdateDTO bean) {

        // 只允许修改状态为结束施工
        if (bean.getStatus() == 2) {
            SporadicProject entity = sporadicProjectRepository.findById(bean.getId())
                    .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

            if (entity.getStatus() == 0) {
                throw new ScException("当前工程施工未开始");
            }

            entity.setStatus(2);

            // 查询当前监管流程工单
            MonitorOrder monitorOrder = monitorOrderService.findByProjectId(entity.getId());
            if (monitorOrder == null) {
                throw new ScException("当前监管流程工单不存在！");
            }
            // 判断当前流程
            if (!monitorOrder.getFlow().equals(MonitorFlowEnum.TYPE5.getType())) {
                throw new ScException("当前不处于施工待完成的流程不允许结束施工！");
            }
            // 查询当前流程环节
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(monitorOrder.getId(),
                    monitorOrder.getFlow());
            MonitorFlowUpdateDTO flowUpdateDTO = new MonitorFlowUpdateDTO();
            flowUpdateDTO.setId(monitorFlow.getId());
            flowUpdateDTO.setState(1);
            // 结束当前流程
            monitorFlowService.update(flowUpdateDTO);

            sporadicProjectRepository.save(entity);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> sporadicProjectRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                sporadicProjectRepository.save(entity);

                // 删除工程时，移除关联的工程人员
                engineeringMembersService.removeMemberRelateByProjectId(entity.getId());

                // 删除工程时，删除工单
                monitorOrderRepository.deleteByProjectId(entity.getId());

            }));
        }
    }

    @Override
    public void export(SporadicProjectQueryDTO bean) {
        List<SporadicProjectRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, SporadicProjectRespDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result<?> importExcel(MultipartFile file) {

        String organizationId = XsgcContext.getOrganizationId();
        if (StringUtil.isBlank(organizationId)) {
            throw new ScException("组织id不能为空！");
        }

        String userId = ScContext.getCurrentUserThrow().getId();

        try {
            List<SporadicProjectExcelDTO> importData;
            int saveNum = 0;
            List<SporadicProjectExcelDTO> errorList = new ArrayList<>();
            try {
                importData = ExportWordUtil2007.importExcel(file, 0, 1, SporadicProjectExcelDTO.class);
            } catch (Exception e) {
                e.printStackTrace();
                throw new ScException("读取excel错误");
            }
            for (SporadicProjectExcelDTO record : importData) {
                SporadicProject entity = handleSingleRecord(record, userId, organizationId);
                if (entity == null) {
                    errorList.add(record);
                    continue;
                }
                entity.setCreateUser(userId);
                entity.setOrganizationId(organizationId);

                // 更新施工状态
                entity.setStatus(getProjectStatus(entity.getStartAt(), entity.getEndAt()));
                SporadicProject save = sporadicProjectRepository.save(entity);

                // 创建或者更新工程成员
                createOrUpdateEngineer(save);

                // 创建监管流程工单
                monitorOrderService.createOrder(save);

                saveNum++;
            }
            int errors = errorList.size();
            if (errors > 0) {
                List<String> cloList = Arrays.asList(
                        "name", "pCateName", "cateName", "amount", "area",
                        "startAt", "endAt", "district", "street", "community", "address",
                        "constructorName", "constructorCharger", "ownerMobile",
                        "contractorName", "contractorCharger", "contractorChargerMobile", "projectNumber",
                        "error");
                String title = "导入失败记录表";
                Workbook workbook = ScExcelExportUtil.exportExcel(new ExportParams(title, title),
                        SporadicProjectExcelDTO.class, errorList, cloList);
                try {
                    String url = uploadWorkbook(workbook, title);
                    return new Result<>("4004",
                            saveNum > 0 ? saveNum + "条数据导入成功," + errors + "条数据导入失败" : errors + "条数据导入失败",
                            url);
                } catch (IOException e) {
                    throw new ScException(e.getMessage());
                }
            }
            return new Result<>("0000", saveNum + "条数据导入成功");
        } catch (Exception e) {
            log.error(e.getMessage());
            throw new ScException("导入失败");
        }
    }

    /**
     * 创建或者更新工程成员
     * 
     * @param bean
     */
    private void createOrUpdateEngineer(SporadicProject bean) {
        // 创建或更新业主
        EngineeringMembersUpdateDTO updateDTO = new EngineeringMembersUpdateDTO();

        // 先删除，再创建
        engineeringMembersService.removeMemberRelateByProjectId(bean.getId());

        // 项目id和组织id
        updateDTO.setProjectId(bean.getId());
        updateDTO.setOrganizationId(bean.getOrganizationId());

        updateDTO.setType(EngineeringRoleEnum.CONSTRUCTION_PARTY.getMemberType());
        updateDTO.setRealname(bean.getConstructorCharger());
        updateDTO.setMobile(bean.getOwnerMobile());
        engineeringMembersService.createOrUpdate(updateDTO);

        // 创建或更新施工单位负责人
        updateDTO.setType(EngineeringRoleEnum.CONSTRUCTION_UNIT_LEADER.getMemberType());
        updateDTO.setRealname(bean.getContractorCharger());
        updateDTO.setMobile(bean.getContractorChargerMobile());
        engineeringMembersService.createOrUpdate(updateDTO);

        // 创建施工单位
        ContractingUnitUpdateDTO contractorUpdateDTO = new ContractingUnitUpdateDTO();
        contractorUpdateDTO.setName(bean.getContractorName());
        ContractingUnit contractor = contractingUnitService.createOrUpdate(contractorUpdateDTO);
        bean.setContractorId(contractor.getId());

        // 创建施工单位组织关联
        ContractingUnitOrganizationRelateUpdateDTO relate = new ContractingUnitOrganizationRelateUpdateDTO();
        relate.setContractingUnitId(contractor.getId());
        relate.setOrganizationId(bean.getOrganizationId());
        contractingUnitOrganizationRelateService.createOrUpdate(relate);
    }

    /**
     * 将 Workbook 上传到 oss
     *
     * @param workbook
     */
    public String uploadWorkbook(Workbook workbook, String title) throws IOException {
        ByteArrayOutputStream stream = uploadUtilV2.transform(workbook);
        String fileName = title + ".xlsx";
        ByteArrayMultipartFile file = new ByteArrayMultipartFile(fileName, null, stream.toByteArray());
        FileUploadResourceDTO resDto = new FileUploadResourceDTO();
        resDto.setName(fileName);
        resDto.setOpen(true);
        resDto.setTarget(0);
        try {
            Result<FileUploadResourceDTO> result = fileService.upload(file, resDto, null);
            if (Objects.isNull(result) || !result.success()) {
                throw new ScException("上传" + fileName + "失败");
            }
            return result.getData().getUrl();
        } catch (Exception e) {
            throw new ScException("上传" + fileName + "失败");
        }
    }

    protected SporadicProject handleSingleRecord(SporadicProjectExcelDTO record, String userId, String organizationId) {
        try {
            Set<ConstraintViolation<SporadicProjectExcelDTO>> errors = validator.validate(record);
            if (!errors.isEmpty()) {
                BizAssert.isTrue(false, errors.iterator().next().getMessage());
            }
            BizAssert.isTrue(record.getEndAt().compareTo(record.getStartAt()) > 0, "项目结束时间必须大于开始时间");
            SporadicProjectCategory cate = sporadicProjectCategoryRepository.findFirstByName(record.getCateName());
            BizAssert.notNull(cate, "工程类别（必填）不存在");
            SporadicProjectCategory pCate = sporadicProjectCategoryRepository.findFirstByName(record.getPCateName());
            BizAssert.notNull(pCate, "工程分类（必填）不存在");
            BizAssert.isFalse(pCate.getId().equals(cate.getPId()), "工程类别不属于此工程分类");

            String regionPid = comRegionRepository.findIdByPidAndTitle(CommonConstant.REGION_SHENZHEN,
                    record.getDistrict());
            BizAssert.notNull(regionPid, "所属区不存在");
            String regionId = comRegionRepository.findIdByPidAndTitle(regionPid, record.getStreet());
            BizAssert.notNull(regionId, "所属街道不存在");
            String regionCid = comRegionRepository.findIdByPidAndTitle(regionId, record.getVillage());
            BizAssert.notNull(regionCid, "所属社区不存在");
            SporadicProject project = sporadicProjectRepository.findFirstByRegionPidAndRegionIdAndRegionCidAndName(
                    regionPid, regionId, regionCid, record.getName());
            BizAssert.isNull(project, "当前区域已存在此工程");
            setLngLat(record);

            SporadicProject entity = new SporadicProject();
            BeanUtil.copyProperties(record, entity);
            entity.setCateId(cate.getId());
            entity.setCatePid(cate.getPId());
            entity.setRegionPid(regionPid);
            entity.setRegionId(regionId);
            entity.setRegionCid(regionCid);

            // 检查当前组织人员所属区域
            if (!checkRegion(userId, organizationId, entity)) {
                throw new ScException("您无当前区域权限！");
            }

            // 新增gis_poi
            if (entity.getLat() != null && entity.getLng() != null) {
                GisPoi gisPoi = new GisPoi();
                gisPoi.setAddr(entity.getAddress());
                gisPoi.setLng(entity.getLng().toString());
                gisPoi.setLat(entity.getLat().toString());
                gisPoi.setCreatedAt(new Date());
                GisPoi save = gisPoiRepository.save(gisPoi);
                entity.setPoiId(save.getId());
            }

            return entity;
        } catch (Exception e) {
            record.setError("新增工程记录异常 - " + e.getMessage());
            return null;
        }
    }

    private void setLngLat(SporadicProjectExcelDTO bean) {
        String address = "深圳市" + bean.getDistrict() + bean.getStreet() + bean.getVillage() + bean.getAddress();
        String location = gaoDeMapUtil.getLngLat(address);
        if (StringUtils.isNotEmpty(location)) {
            String[] locs = location.split(",");
            bean.setLng(new BigDecimal(locs[0]));
            bean.setLat(new BigDecimal(locs[1]));
        }
    }

    @Transactional
    @Override
    public void updateMonitorFlag(String projectId) {
        // 查询当前项目是否有关联
        sporadicProjectRepository.updateMonitorFlagById(projectId);
    }

    public List<SporadicProjectRespDTO> mobileSelect() {

        SporadicProjectMobileQueryDTO bean = new SporadicProjectMobileQueryDTO();

        List<SporadicProjectRespDTO> list = sporadicProjectRepository.mobileIndex(bean);
        return list;
    }

    @Override
    public Page<SporadicProjectRespDTO> mobileIndex(SporadicProjectMobileQueryDTO bean) {

        SporadicProjectQueryDTO dto = new SporadicProjectQueryDTO();
        BeanUtils.copyProperties(bean, dto);

        UtilsRegion region = userUtil.new UtilsRegion();
        region.setRegionPid(dto.getRegionPid());
        region.setRegionId(dto.getRegionId());
        region.setRegionCid(dto.getRegionCid());
        UserUtilsProjectQueryDTO userProjectQueryDTO = userUtil.createUserProjectDto(region).orElse(null);

        if (userProjectQueryDTO == null) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        BeanUtils.copyProperties(userProjectQueryDTO, bean);

        Page<SporadicProjectRespDTO> page = sporadicProjectRepository.mobileIndex(bean,
                PageRequest.of(bean.getPage(), bean.getSize()));
        return page;
    }

    /**
     * 自动更新施工状态
     */
    @Override
    @Transactional
    public void autoUpdateStatus() {

        Date yesterday = DateUtil.yesterday();
        Date today = new Date();

        // 查询需要开始施工的项目
        List<SporadicProject> needToStart = sporadicProjectRepository.findByStartAtLessThanAndStatus(today, 0);
        // 查询需要结束施工的项目
        List<SporadicProject> needToEnd = sporadicProjectRepository.findByEndAtLessThanAndStatus(yesterday, 1);

        for (SporadicProject sporadicProject : needToStart) {
            sporadicProject.setStatus(1);
        }
        sporadicProjectRepository.saveAll(needToStart);

        // 需要结束的项目
        for (SporadicProject sporadicProject : needToEnd) {
            // 查询工单
            MonitorOrder monitorOrder = monitorOrderService.findByProjectId(sporadicProject.getId());

            try {
                if (monitorOrder != null) {

                    // 查询当前环节
                    MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(monitorOrder.getId(),
                            monitorOrder.getFlow());

                    if (monitorOrder.getFlow().equals(MonitorFlowEnum.TYPE5.getType())) {

                        // 如果当前处于施工完成状态
                        MonitorFlowUpdateDTO updateDTO = new MonitorFlowUpdateDTO();
                        updateDTO.setId(monitorFlow.getId());
                        updateDTO.setState(1);
                        monitorFlowService.update(updateDTO);
                    }
                    // 这个逻辑有问题
                    // else {
                    // //如果未走到施工完成状态，也需要施工完成
                    // if (monitorOrder.getFlow()<MonitorFlowEnum.TYPE5.getType()){
                    // //删除当前节点
                    // monitorFlowRepository.delete(monitorFlow);
                    //
                    // //创建施工完成节点
                    // MonitorFlowCreateDTO flowCreateDTO = new MonitorFlowCreateDTO();
                    // flowCreateDTO.setFlow(MonitorFlowEnum.TYPE5.getType());
                    // flowCreateDTO.setOrderId(monitorOrder.getId());
                    // flowCreateDTO.setState(0);
                    // flowCreateDTO.setProjectId(monitorOrder.getProjectId());
                    // MonitorFlow monitorFlowType5 = monitorFlowService.create(flowCreateDTO);
                    // //结束施工完成的节点
                    //
                    // MonitorFlowUpdateDTO updateDTO = new MonitorFlowUpdateDTO();
                    // updateDTO.setId(monitorFlowType5.getId());
                    // updateDTO.setState(1);
                    // monitorFlowService.update(updateDTO);
                    // }
                    // }

                }
                // 修改工程状态为施工完成
                sporadicProject.setStatus(2);
            } catch (Exception e) {
                e.printStackTrace();
                XxlJobHelper.log("关闭施工状态失败！工程id：{},错误原因:{}", sporadicProject.getId(), e.getMessage());
            }

        }

    }

    @Override
    @Transactional
    public void autoUpdateMonitorFlag() {

        // 当前处于移除监控环节的
        List<MonitorOrder> monitorOrderList = monitorOrderRepository.findByFlow(MonitorFlowEnum.TYPE8.getType());
        for (MonitorOrder monitorOrder : monitorOrderList) {
            // 查询是否存在监控设备
            boolean ifHasDevice = eventCameraPointDeviceRepository.existsByProjectId(monitorOrder.getProjectId());
            if (!ifHasDevice) {
                // 如果已经没有关联的监控了，就更新监管接入状态
                Optional<SporadicProject> projectOptional = sporadicProjectRepository
                        .findById(monitorOrder.getProjectId());
                if (projectOptional.isPresent()) {
                    SporadicProject sporadicProject = projectOptional.get();

                    // 已结束监管
                    sporadicProject.setMonitorFlag(2);
                    sporadicProjectRepository.save(sporadicProject);

                    try {
                        MonitorFlowUpdateDTO flowUpdateDTO = new MonitorFlowUpdateDTO();
                        flowUpdateDTO.setId(monitorOrder.getFlowId());
                        flowUpdateDTO.setState(1);
                        monitorFlowService.update(flowUpdateDTO);
                    } catch (Exception e) {
                        XxlJobHelper.log("更新流程节点失败！工单id：{}", monitorOrder.getId());
                    }

                }

            }
        }

    }

    /**
     * 获取用户对应的项目列表
     */
    public List<SporadicProjectRespDTO> getUserProjectList(UtilsRegion region, SporadicProjectQueryDTO dto) {

        UserUtilsProjectQueryDTO queryDTO = userUtil.createUserProjectDto(region).orElse(null);

        if (queryDTO == null) {
            return new ArrayList<>();
        }

        SporadicProjectMobileQueryDTO projectQueryDTO = new SporadicProjectMobileQueryDTO();

        if (dto != null) {
            BeanUtils.copyProperties(dto, projectQueryDTO);
        }

        BeanUtils.copyProperties(queryDTO, projectQueryDTO);

        List<SporadicProjectRespDTO> sporadicProjects = sporadicProjectRepository.queryProjects(projectQueryDTO);

        return sporadicProjects;
    }

    /**
     * 获取用户对应的项目列表
     */
    public List<SporadicProjectRespDTO> getUserProjectList(SporadicProjectQueryDTO dto) {
        return getUserProjectList(userUtil.new UtilsRegion(), dto);
    }

    /**
     * 获取用户对应的项目列表
     */
    public List<SporadicProjectRespDTO> getUserProjectList() {
        return getUserProjectList(userUtil.new UtilsRegion(), null);
    }

}