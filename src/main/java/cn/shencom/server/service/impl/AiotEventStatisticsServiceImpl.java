package cn.shencom.server.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.shencom.model.AimFirmSceneManagement;
import cn.shencom.model.AimSceneManagement;
import cn.shencom.model.AiotEventStatistics;
import cn.shencom.model.dto.create.AiotEventStatisticsCreateDTO;
import cn.shencom.model.dto.query.AiotEventQueryDTO;
import cn.shencom.model.dto.query.AiotEventStatisticsQueryDTO;
import cn.shencom.model.dto.resp.AiotEventRespDTO;
import cn.shencom.model.dto.resp.AiotEventStatisticsRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.AiotEventStatisticsUpdateDTO;
import cn.shencom.repos.AimFirmSceneManagementRepository;
import cn.shencom.repos.AiotEventRepository;
import cn.shencom.repos.AiotEventStatisticsRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.server.service.IAiotEventStatisticsService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.utils.UserUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 事件统计分类配置表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Service
@Slf4j
public class AiotEventStatisticsServiceImpl extends BaseImpl implements IAiotEventStatisticsService {

    @Autowired
    private AiotEventStatisticsRepository aiotEventStatisticsRepository;

    @Autowired
    private AimFirmSceneManagementRepository aimFirmSceneManagementRepository;

    @Autowired
    private AiotEventRepository aiotEventRepository;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Autowired
    private UserUtil userUtil;

    @Override
    public Page<AiotEventStatisticsRespDTO> query(AiotEventStatisticsQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotEventStatistics.class);
        Page<AiotEventStatistics> res = aiotEventStatisticsRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));

        List<String> sceneCodeList = res.getContent().stream().map(AiotEventStatistics::getSceneCode)
                .collect(Collectors.toList());

        Pair<Map<String, AimFirmSceneManagement>, Map<String, AimSceneManagement>> sceneTypeAndName = setSceneTypeAndName(
                sceneCodeList);

        return ScQueryUtil.handle(res, linkMap, AiotEventStatisticsRespDTO.class, (r, dto) -> {

            AimFirmSceneManagement sceneManagement = sceneTypeAndName.getKey().get(r.getSceneCode());
            if (sceneManagement != null) {
                dto.setIssueName(sceneManagement.getSceneName());
            }

            AimSceneManagement sceneType = sceneTypeAndName.getValue().get(r.getSceneCode());
            if (sceneType != null) {
                dto.setTypeName(sceneType.getName());
            }

        });
    }

    @Override
    public AiotEventStatisticsRespDTO show(ScShowDTO bean) {
        Optional<AiotEventStatistics> option = aiotEventStatisticsRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AiotEventStatistics entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotEventStatistics.class);
        return ScQueryUtil.handleOne(entity, linkMap, AiotEventStatisticsRespDTO.class, (r, dto) -> {

            // 根据违规类型编号 获取厂商-> 获取AI场景
            String sceneName = aimFirmSceneManagementRepository.getSceneName(dto.getSceneCode()).stream()
                    .filter(Objects::nonNull).map(String::valueOf).collect(Collectors.joining(","));
            dto.setTypeName(sceneName);

            AimFirmSceneManagement sceneManagement = aimFirmSceneManagementRepository.findFirstByCode(r.getSceneCode());
            if (sceneManagement != null) {
                dto.setIssueName(sceneManagement.getSceneName());
            }

        });
    }

    /**
     * 设置场景类别和场景名称
     */
    private Pair<Map<String, AimFirmSceneManagement>, Map<String, AimSceneManagement>> setSceneTypeAndName(
            List<String> sceneCodeList) {
        if (CollectionUtils.isEmpty(sceneCodeList)) {
            return new Pair<>(new HashMap<>(), new HashMap<>());
        }

        List<AimFirmSceneManagement> sceneManagementList = aimFirmSceneManagementRepository
                .findAllByCodeIn(sceneCodeList);

        Map<String, AimFirmSceneManagement> sceneManagementMap = sceneManagementList.stream()
                .collect(Collectors.toMap(AimFirmSceneManagement::getCode, Function.identity()));

        // 根据违规类型编号 获取厂商-> 获取AI场景
        List<Map<String, Object>> sceneTypeManagement = aimFirmSceneManagementRepository
                .getSceneNameByCodeIn(sceneCodeList).stream()
                // code 去重
                .collect(Collectors.groupingBy(map -> String.valueOf(map.get("code")))).values().stream()
                .map(list -> list.get(0)).collect(Collectors.toList());

        Map<String, AimSceneManagement> sceneTypeManagementMap = sceneTypeManagement.stream()
                .collect(Collectors.toMap(map -> String.valueOf(map.get("code")), map -> {
                    AimSceneManagement aimSceneManagement = new AimSceneManagement();
                    aimSceneManagement.setId(String.valueOf(map.get("id")));
                    aimSceneManagement.setName(String.valueOf(map.get("name")));
                    aimSceneManagement.setCode(String.valueOf(map.get("code")));
                    return aimSceneManagement;
                }));

        return new Pair<>(sceneManagementMap, sceneTypeManagementMap);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotEventStatistics create(AiotEventStatisticsCreateDTO bean) {
        AiotEventStatistics entity = new AiotEventStatistics();
        BeanUtil.copyProperties(bean, entity);
        return aiotEventStatisticsRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotEventStatistics update(AiotEventStatisticsUpdateDTO bean) {
        AiotEventStatistics entity = aiotEventStatisticsRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aiotEventStatisticsRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aiotEventStatisticsRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                aiotEventStatisticsRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(AiotEventStatisticsQueryDTO bean) {
        List<AiotEventStatisticsRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AiotEventStatisticsRespDTO.class);
    }

    /**
     * 同步事件统计数据
     */
    private void syncEventStatistics(AiotEventStatisticsQueryDTO bean, List<String> projectIds) {
        Date startDate = bean.getStartDate();
        Date endDate = bean.getEndDate();

        // 开始时间比结束时间大, 设置为空
        if (startDate != null && startDate.after(endDate)) {
            startDate = null;
            endDate = null;
        }

        if (startDate == null || endDate == null) {
            return;
        }

        if (CollectionUtils.isEmpty(projectIds)) {
            return;
        }

        AiotEventQueryDTO aiotEventQueryDTO = new AiotEventQueryDTO();
        aiotEventQueryDTO.setStartDate(startDate);
        aiotEventQueryDTO.setEndDate(endDate);
        aiotEventQueryDTO.setProjectId(projectIds);
        List<AiotEventRespDTO> eventList = aiotEventRepository.queryEvent(aiotEventQueryDTO);
        // 通过 sceneCode 和 eventDate 和 projectId 进行 toMap, key
        // `sceneCode_eventDate_projectId`, 在进行 id 统计数量
        Map<String, Map<Date, Map<String, Long>>> eventMap = eventList.stream()
                .collect(Collectors.groupingBy(AiotEventRespDTO::getSceneCode,
                        Collectors.groupingBy(event -> DateUtil.beginOfDay(event.getEventAt()).toJdkDate(),
                                Collectors.groupingBy(AiotEventRespDTO::getProjectId, Collectors.counting()))));

        // 通过 sceneCode 和 eventDate 查询
        List<AiotEventStatistics> aiotEventStatisticsList = aiotEventStatisticsRepository
                .queryByEventDateBetweenAndProjectIdIn(startDate, endDate, projectIds);

        // 将现有统计数据转换为Map，方便查找和更新
        Map<String, AiotEventStatistics> statisticsMap = new HashMap<>();
        for (AiotEventStatistics statistics : aiotEventStatisticsList) {
            String key = statistics.getSceneCode() + "_" + DateUtil.formatDate(statistics.getEventDate()) + "_"
                    + statistics.getProjectId();
            statisticsMap.put(key, statistics);
        }

        List<AiotEventStatistics> toSaveList = new ArrayList<>();

        // 遍历事件映射，更新或创建统计记录
        for (Map.Entry<String, Map<Date, Map<String, Long>>> sceneEntry : eventMap.entrySet()) {
            String sceneCode = sceneEntry.getKey();
            Map<Date, Map<String, Long>> dateProjectCountMap = sceneEntry.getValue();

            for (Map.Entry<Date, Map<String, Long>> dateEntry : dateProjectCountMap.entrySet()) {
                Date eventDate = dateEntry.getKey();
                Map<String, Long> projectCountMap = dateEntry.getValue();

                for (Map.Entry<String, Long> projectEntry : projectCountMap.entrySet()) {
                    String projectId = projectEntry.getKey();
                    Long count = projectEntry.getValue();
                    String key = sceneCode + "_" + DateUtil.formatDate(eventDate) + "_" + projectId;

                    AiotEventStatistics statistics = statisticsMap.get(key);
                    if (statistics == null) {
                        // 如果不存在，则创建新记录
                        statistics = new AiotEventStatistics();
                        statistics.setSceneCode(sceneCode);
                        statistics.setEventDate(eventDate);
                        statistics.setProjectId(projectId);
                        statistics.setIsDeleted(0);
                    }

                    // 更新事件计数
                    statistics.setEventCount(count.intValue());
                    toSaveList.add(statistics);

                    // 从Map中移除已处理的记录
                    statisticsMap.remove(key);
                }
            }
        }

        // 处理剩余的记录（在数据库中存在但在事件中不存在的记录）
        for (AiotEventStatistics statistics : statisticsMap.values()) {
            statistics.setEventCount(0); // 设置为0
            toSaveList.add(statistics);
        }

        // 批量保存所有更新和新建的记录
        if (!toSaveList.isEmpty()) {
            aiotEventStatisticsRepository.saveAll(toSaveList);
        }
    }

    /**
     * 统计事件数量
     */
    @Override
    public List<AiotEventStatisticsRespDTO> count(AiotEventStatisticsQueryDTO bean) {
        List<SporadicProjectRespDTO> projectList = sporadicProjectService.getUserProjectList();

        List<String> projectIds = projectList.stream().map(SporadicProjectRespDTO::getId).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(projectIds)) {
            return new ArrayList<>();
        }

        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotEventStatistics.class);

        Date startDate = bean.getStartDate();
        Date endDate = bean.getEndDate();

        if (bean.getIsSync() != null && bean.getIsSync()) {
            if (startDate == null || endDate == null) {
                throw new ScException("开始日期和结束日期不能为空");
            }
            syncEventStatistics(bean, projectIds);
        }

        List<AiotEventStatistics> list = aiotEventStatisticsRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (startDate != null && endDate != null) {
                ps.add(builder.between(root.get("eventDate"), startDate, endDate));
            }
            if (CollectionUtils.isNotEmpty(projectIds)) {
                ps.add(builder.in(root.get("projectId")).value(projectIds));
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        });

        // 只按照 sceneCode 分组，累加所有日期的 eventCount
        Map<String, Integer> sceneCodeTotalCountMap = list.stream()
                .collect(Collectors.groupingBy(AiotEventStatistics::getSceneCode,
                        Collectors.summingInt(AiotEventStatistics::getEventCount)));

        List<AiotEventStatisticsRespDTO> res = new ArrayList<>();

        List<String> sceneCodeList = new ArrayList<>(sceneCodeTotalCountMap.keySet());
        Pair<Map<String, AimFirmSceneManagement>, Map<String, AimSceneManagement>> sceneTypeAndName = setSceneTypeAndName(
                sceneCodeList);

        // 直接遍历 sceneCodeTotalCountMap，为每个 sceneCode 创建一个 DTO
        sceneCodeTotalCountMap.forEach((sceneCode, totalEventCount) -> {
            AiotEventStatisticsRespDTO dto = new AiotEventStatisticsRespDTO();
            dto.setSceneCode(sceneCode);
            dto.setEventCount(totalEventCount);

            AimFirmSceneManagement sceneManagement = sceneTypeAndName.getKey().get(sceneCode);
            if (sceneManagement != null) {
                dto.setIssueName(sceneManagement.getSceneName());
            }

            AimSceneManagement sceneType = sceneTypeAndName.getValue().get(sceneCode);
            if (sceneType != null) {
                dto.setTypeName(sceneType.getName());
            }

            res.add(dto);
        });

        // eventCount 排序
        res.sort(Comparator.comparing(AiotEventStatisticsRespDTO::getEventCount).reversed());

        return res;
    }

    /**
     * 更新事件统计
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateEventStatistics(String sceneCode, Date eventDate, List<String> projectIds) {
        AiotEventStatisticsQueryDTO bean = new AiotEventStatisticsQueryDTO();
        bean.setSceneCode(sceneCode);
        bean.setStartDate(DateUtil.beginOfDay(eventDate).toJdkDate());
        bean.setEndDate(DateUtil.endOfDay(eventDate).toJdkDate());
        syncEventStatistics(bean, projectIds);
    }
}