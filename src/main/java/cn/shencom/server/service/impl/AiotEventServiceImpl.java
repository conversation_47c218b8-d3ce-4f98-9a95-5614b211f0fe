package cn.shencom.server.service.impl;

import cn.shencom.constant.CommonConstant;
import cn.shencom.enums.MessageRemindEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.AiotEventPushDTO;
import cn.shencom.model.dto.XsgcMessageRemindDTO;
import cn.shencom.model.dto.create.AiotEventCreateDTO;
import cn.shencom.model.dto.query.AiotEventMobileQueryDTO;
import cn.shencom.model.dto.query.AiotEventQueryDTO;
import cn.shencom.model.dto.query.SporadicProjectQueryDTO;
import cn.shencom.model.dto.resp.AiotEventMobileRespDTO;
import cn.shencom.model.dto.resp.AiotEventRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.update.AiotEventUpdateDTO;
import cn.shencom.rabbitmq.sender.XsgcMessageRemindSender;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.scpage.ScQuery;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.IAiotCategoryService;
import cn.shencom.server.service.IAiotEventService;
import cn.shencom.server.service.IAiotEventStatisticsService;
import cn.shencom.server.service.IAiotSceneService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.utils.UserUtil;
import cn.shencom.utils.UserUtil.UserUtilsProjectQueryDTO;
import cn.shencom.utils.UserUtil.UtilsRegion;
import cn.shencom.utils.XsgcContext;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.annotation.Resource;
import javax.persistence.criteria.*;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 监控事件 的服务实现
 *
 * <AUTHOR>
 * @since 2025-06-10
 */
@Service
@Slf4j
public class AiotEventServiceImpl extends BaseImpl implements IAiotEventService {

    @Autowired
    private AiotEventRepository aiotEventRepository;

    @Resource
    private IAiotCategoryService aiotCategoryService;

    @Resource
    private IAiotSceneService aiotSceneService;

    @Resource
    private SporadicProjectRepository sporadicProjectRepository;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private AimFirmSceneManagementRepository aimFirmSceneManagementRepository;

    @Autowired
    private IAiotEventStatisticsService aiotEventStatisticsService;

    @Autowired
    private UserUtil userUtil;

    @Autowired
    private XsgcMessageRemindSender xsgcMessageRemindSender;

    @Override
    public Page<AiotEventRespDTO> query(AiotEventQueryDTO bean) {

        String organizationId = XsgcContext.getOrganizationId();

        List<SporadicProjectRespDTO> projectList = sporadicProjectService.getUserProjectList();
        List<String> projectIds = projectList.stream().map(SporadicProjectRespDTO::getId).distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(projectIds)) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        // 人工智能管理下的事件列表
        String sceneId = bean.getSceneId();
        if (StringUtils.isBlank(sceneId)) {
            sceneId = ScQueryUtil.getValueAndRm(bean.getQuery(), "sceneId");
        }

        // 场景类别
        String sceneCategoryId = bean.getSceneCategoryId();
        if (StringUtils.isBlank(sceneCategoryId)) {
            sceneCategoryId = ScQueryUtil.getValueAndRm(bean.getQuery(), "sceneCategoryId");
        }

        // 违规类型编号
        List<String> typeCodeList = Lists.newArrayList();
        if (StringUtils.isNotBlank(sceneId)) {
            // 该场景关联的厂商场景
            List<String> codeBySceneId = aimFirmSceneManagementRepository.getCodeBySceneId(sceneId);
            if (codeBySceneId.isEmpty()) {
                return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
            }
            typeCodeList.addAll(codeBySceneId);
        }
        // 根据场景配置筛选
        if (StringUtils.isNotBlank(sceneCategoryId)) {
            List<String> codesById = aimFirmSceneManagementRepository.getCodeBySceneCategoryId(sceneCategoryId);
            typeCodeList.addAll(codesById);
            if (codesById.isEmpty()) {
                return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
            }
        }

        List<ScQuery> queries = bean.getQuery();
        String projectName = ScQueryUtil.getValueAndRm(queries, "projectName");

        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotEvent.class);
        Page<AiotEvent> res = aiotEventRepository.findAll((root, query, builder) -> {
            // 用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            Join<Object, Object> projectJoin = root.join("project", JoinType.INNER);
            ps.add(builder.in(projectJoin.get("id")).value(projectIds));

            if (!typeCodeList.isEmpty()) {
                ps.add(builder.in(root.get("sceneCode")).value(typeCodeList));
            }

            if (StringUtils.isNotEmpty(projectName)) {
                ps.add(builder.like(projectJoin.get("name"), "%" + projectName + "%"));
            }

            if (StringUtils.isNotEmpty(organizationId)) {
                ps.add(builder.equal(projectJoin.get("organizationId"), organizationId));
            }
            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, AiotEventRespDTO.class, this::handleOne);
    }

    @Override
    public AiotEventRespDTO show(ScShowDTO bean) {
        Optional<AiotEvent> option = aiotEventRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        AiotEvent entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(AiotEvent.class);

        AiotEventRespDTO dto = ScQueryUtil.handleOne(entity, linkMap, AiotEventRespDTO.class, this::handleOne);

        // 填充 AI场景 typeName
        AimFirmSceneManagement sceneManagement = aimFirmSceneManagementRepository.findFirstByCode(dto.getSceneCode());
        if (sceneManagement != null) {
            dto.setTypeName(sceneManagement.getSceneName());
        }
        // 填充违规类型
        StringBuilder sb = new StringBuilder();
        // 根据违规类型编号 获取厂商-> 获取AI场景
        List<String> sceneNames = aimFirmSceneManagementRepository.getSceneName(dto.getSceneCode());
        if (CollectionUtils.isNotEmpty(sceneNames)) {
            for (String sceneName : sceneNames) {
                sb.append(sceneName).append(",");
            }
            dto.setSceneName(sb.substring(0, sb.length() - 1));
        }

        // 填充场景类型
        List<String> sceneCategoryNameList = aimFirmSceneManagementRepository.getSceneCategoryName(dto.getSceneCode());
        if (CollectionUtils.isNotEmpty(sceneCategoryNameList)) {
            sb = new StringBuilder();
            for (String sceneCategoryName : sceneCategoryNameList) {
                sb.append(sceneCategoryName).append(",");
            }
            dto.setSceneCategoryName(sb.substring(0, sb.length() - 1));
        }

        return dto;
    }

    private void handleOne(AiotEvent event, AiotEventRespDTO dto) {
        // List<String> sceneCates = new ArrayList<>();
        // List<String> aiScenes = new ArrayList<>();
        // for (AiotCategory cate : event.getSceneCategories()) {
        // if(cate.getLevel() == 0){
        // sceneCates.add(cate.getName());
        // }else if (cate.getLevel() == 1){
        // aiScenes.add(cate.getName());
        // }
        // }
        // dto.setSceneCateNames(sceneCates);
        // dto.setAiSceneNames(aiScenes);

        // 填充 AI场景 typeName
        AimFirmSceneManagement sceneManagement = aimFirmSceneManagementRepository.findFirstByCode(event.getSceneCode());
        if (sceneManagement != null) {
            dto.setTypeName(sceneManagement.getSceneName());
        }

    }

    private void dealScene(AiotEventRespDTO eventRespDTO, Map<String, AimSceneCategoryManagement> aimSceneCategoryMap) {

        // 查询场景类别
        List<String> sceneCategoryId = aimFirmSceneManagementRepository.getSceneCategoryId(eventRespDTO.getSceneCode());
        List<String> sceneCategoryNameList = new ArrayList<>();
        if (!sceneCategoryId.isEmpty()) {
            for (String cateId : sceneCategoryId) {
                if (aimSceneCategoryMap.containsKey(cateId)) {
                    sceneCategoryNameList.add(aimSceneCategoryMap.get(cateId).getName());
                }
            }
        }
        eventRespDTO.setSceneCategoryName(String.join(",", sceneCategoryNameList));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotEvent create(AiotEventCreateDTO bean) {
        AiotEvent entity = new AiotEvent();
        BeanUtil.copyProperties(bean, entity);
        return aiotEventRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotEvent update(AiotEventUpdateDTO bean) {
        AiotEvent entity = aiotEventRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return aiotEventRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> aiotEventRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                entity.setDeletedAt(new Date());
                aiotEventRepository.save(entity);
                aiotEventStatisticsService.updateEventStatistics(entity.getSceneCode(), entity.getEventAt(),
                        new ArrayList<>(Arrays.asList(entity.getProjectId())));
            }));
        }
    }

    @Override
    public void export(AiotEventQueryDTO bean) {
        List<AiotEventRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, AiotEventRespDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public AiotEvent updateOrCreate(AiotEventPushDTO bean) {
        // //先查询有没有用场景和场景分类，没有则创建并关联
        // if(CollectionUtils.isNotEmpty(bean.getSceneCategories())){
        // aiotCategoryService.batchUpdateOrCreateAiCategories(bean.getSceneCategories(),bean.getSceneCode());
        // }
        // if (CollectionUtils.isNotEmpty(bean.getAiScenes())) {
        // aiotCategoryService.batchUpdateOrCreateAiScenes(bean.getAiScenes(),
        // bean.getSceneCode());
        // }

        // 创建场景数据
        // aiotSceneService.updateOrCreate(bean.getSceneCode(), bean.getSceneName());

        AiotEvent entity = aiotEventRepository.findFirstByEventNo(bean.getEventNo());

        boolean isNew = false;
        if (Objects.isNull(entity)) {
            entity = new AiotEvent();
            isNew = true;
            BeanUtil.copyProperties(bean, entity);
        }
        SporadicProjectRespDTO project = sporadicProjectRepository.getProjectBySipUserIdAndChannel(bean.getDeviceCode(),
                bean.getChannelCode());
        if (Objects.nonNull(project)) {
            entity.setProjectId(project.getId());
            entity.setRegionPid(project.getRegionPid());
            entity.setRegionId(project.getRegionId());
            entity.setRegionCid(project.getRegionCid());
            aiotEventStatisticsService.updateEventStatistics(entity.getSceneCode(), entity.getEventAt(),
                    new ArrayList<>(Arrays.asList(project.getId())));

        }

        // entity.setSceneName(bean.getSceneName());
        //
        // //创建监控事件
        // try {
        // aiotEventService.createEventOrder(entity);
        // }catch (Exception e){
        // e.printStackTrace();
        // }

        AiotEvent save = aiotEventRepository.save(entity);

        if (isNew && save.getProjectId() != null) {
            // 事务提交后，送消息
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    XsgcMessageRemindDTO remindDTO = new XsgcMessageRemindDTO();
                    remindDTO.setType(MessageRemindEnum.TYPE8.getType());
                    remindDTO.setEventId(save.getId());
                    remindDTO.setProjectId(save.getProjectId());
                    xsgcMessageRemindSender.send(remindDTO);
                }
            });
        }

        return save;
    }

    //
    // @Transactional
    // public void createEventOrder(AiotEvent bean){
    // EventOrder eventOrder =
    // eventOrderRepository.findFirstByEventCode(bean.getEventNo());
    // if (eventOrder==null){
    // eventOrder = new EventOrder();
    // eventOrder.setProjectId(bean.getProjectId());
    // eventOrder.setRegionPid(bean.getRegionPid());
    // eventOrder.setRegionId(bean.getRegionId());
    // eventOrder.setRegionCid(bean.getRegionCid());
    // eventOrder.setEventCode(bean.getEventNo());
    // eventOrder.setPic(bean.getPics());
    // eventOrder.setEventedAt(bean.getEventAt());
    // if (!StringUtil.isBlank(bean.getViolationBox())){
    // JSONArray array = JSONArray.parseArray(bean.getViolationBox());
    // eventOrder.setViolationAreas(JSONObject.toJSONString(array.get(0)));
    // }
    // eventOrder.setCameraNo(bean.getMonitorNo());
    // eventOrder.setTypeCode(bean.getSceneCode());
    // eventOrder.setTypeName(bean.getSceneName());
    // eventOrder.setOrigin(2);
    // eventOrder.setEventSource(13);
    // eventOrderRepository.save(eventOrder);
    // }
    // }

    @Override
    public void push(AiotEventPushDTO bean) {
        rabbitTemplate.convertAndSend(CommonConstant.AIOT_EVENT_QUEUE, "aiot:event", JSON.toJSONString(bean));
    }

    @Override
    public Page<AiotEventMobileRespDTO> mobileIndex(AiotEventMobileQueryDTO bean) {
        // 获取用户信息
        SecurityUser currentUser = ScContext.getCurrentUser();
        if (currentUser == null) {
            throw new ScException(RespCode.CURRENT_USER_NOT_EXIST);
        }

        String organizationId = XsgcContext.getOrganizationId();

        bean.setProjectIdSet(new HashSet<>());

        UtilsRegion region = userUtil.new UtilsRegion();
        region.setRegionPid(bean.getRegionPid());
        region.setRegionId(bean.getRegionId());
        region.setRegionCid(bean.getRegionCid());

        if (StringUtils.isNotBlank(bean.getProjectId())) {
            region.setProjectId(bean.getProjectId());
        }

        if (StringUtils.isNotBlank(organizationId)) {
            bean.setOrganizationId(organizationId);
        }

        UserUtilsProjectQueryDTO projectQueryDTO = userUtil.createUserProjectDto(region).orElse(null);

        if (projectQueryDTO == null) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        if (bean.getEndTime() == null) {
            bean.setEndTime(LocalDate.now());
        }

        // 如果开始时间大于结束时间则移除开始时间
        if (bean.getStartTime() != null && bean.getStartTime().isAfter(bean.getEndTime())) {
            bean.setStartTime(null);
        }

        if (StringUtils.isNotBlank(bean.getKeyword())) {
            bean.setKeyword("%" + bean.getKeyword() + "%");
        }

        if (StringUtils.isNotBlank(bean.getEventAtSort())) {
            bean.setEventAtSort(String.valueOf(bean.getEventAtSort()).toLowerCase());
        }

        bean.setRegionPidSet(projectQueryDTO.getRegionPidSet());
        bean.setRegionIdSet(projectQueryDTO.getRegionIdSet());
        bean.setRegionCidSet(projectQueryDTO.getRegionCidSet());

        PageRequest pageable = PageRequest.of(bean.getPage(), bean.getSize());

        Page<AiotEventMobileRespDTO> data = aiotEventRepository.mobileIndex(bean, pageable);
        Page<AiotEventMobileRespDTO> res = ScQueryUtil.handle(data, null, AiotEventMobileRespDTO.class,
                (r, dto) -> {
                    AimFirmSceneManagement sceneManagement = aimFirmSceneManagementRepository
                            .findFirstByCode(r.getSceneCode());
                    if (sceneManagement != null) {
                        dto.setTypeName(sceneManagement.getSceneName());
                    }
                });
        return res;
    }
}