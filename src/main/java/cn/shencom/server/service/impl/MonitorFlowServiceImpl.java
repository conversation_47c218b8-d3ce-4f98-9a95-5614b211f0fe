package cn.shencom.server.service.impl;

import cn.shencom.enums.MessageRemindEnum;
import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.XsgcMessageRemindDTO;
import cn.shencom.model.dto.create.*;
import cn.shencom.model.dto.query.MonitorFlowQueryDTO;
import cn.shencom.model.dto.resp.*;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.rabbitmq.sender.XsgcMessageRemindSender;
import cn.shencom.repos.MonitorAccessInfoRepository;
import cn.shencom.repos.MonitorFlowRepository;
import cn.shencom.repos.MonitorOrderRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.klock.annotation.Klock;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单流程 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorFlowServiceImpl extends BaseImpl implements IMonitorFlowService {

    @Autowired
    private MonitorFlowRepository monitorFlowRepository;

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;


    @Autowired
    private IMonitorInstallReservationService monitorInstallReservationService;

    @Autowired
    private IMonitorOnSceneInspectionService monitorOnSceneInspectionService;

    @Autowired
    private IMonitorInstallService monitorInstallService;

    @Autowired
    private IMonitorRecycleReservationService monitorRecycleReservationService;

    @Autowired
    private IMonitorRecycleService monitorRecycleService;

    @Autowired
    private MonitorAccessInfoRepository monitorAccessInfoRepository;

    @Autowired
    private XsgcMessageRemindSender xsgcMessageRemindSender;


    @Override
    public Page<MonitorFlowRespDTO> query(MonitorFlowQueryDTO bean) {
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorFlow.class);
        Page<MonitorFlow> res = monitorFlowRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorFlowRespDTO.class, this::delData);
    }


    /**
     * 填充详细信息
     * @param re
     * @param dto
     */
    private void delData(MonitorFlow re,MonitorFlowRespDTO dto){
        String relateId = re.getRelateId();
        Integer flow = re.getFlow();
        ScShowDTO scShowDTO = new ScShowDTO();
        scShowDTO.setId(relateId);
        if (flow.equals(MonitorFlowEnum.TYPE1.getType())){
            if (relateId!=null){
                //预约回收
                MonitorInstallReservationRespDTO entity = monitorInstallReservationService.show(scShowDTO);
                if (entity!=null){
                    dto.setOrderId(re.getOrderId());
                    dto.setContactMobile(entity.getContactMobile());
                    dto.setContactName(entity.getContactName());
                    dto.setReservationTime(entity.getReservationTime());
                }
            }


        }else if (flow.equals(MonitorFlowEnum.TYPE2.getType())){
            //现场勘察
            if (relateId!=null){
                MonitorOnSceneInspectionRespDTO entity = monitorOnSceneInspectionService.show(scShowDTO);
                if (entity!=null){
                    dto.setOrderId(entity.getOrderId());
                    dto.setContactMobile(entity.getContactMobile());
                    dto.setContactName(entity.getContactName());
                    dto.setMemo(entity.getMemo());
                    dto.setContactName(entity.getContactName());
                    dto.setContactMobile(entity.getContactMobile());
                    dto.setInspectTime(entity.getInspectTime());
                    dto.setReservationTime(entity.getReservationTime());
                    dto.setInstallCnt(entity.getInstallCnt());
                    dto.setPic(entity.getPic());
                }
            }


        }else if (flow.equals(MonitorFlowEnum.TYPE3.getType())){
            //上门安装
            if (relateId!=null){
                MonitorInstallRespDTO entity = monitorInstallService.show(scShowDTO);
                if (entity!=null){
                    dto.setOrderId(entity.getOrderId());
                    dto.setContactMobile(entity.getContactMobile());
                    dto.setContactName(entity.getContactName());
                    dto.setMemo(entity.getMemo());
                    dto.setInstallTime(entity.getInstallTime());
                    dto.setInstallCnt(entity.getInstallCnt());
                    dto.setPic(entity.getPic());
                }
            }

        }else if (flow.equals(MonitorFlowEnum.TYPE4.getType())){
            //接入监管， 摄像头详情
            List<MonitorAccessInfo> monitorAccessInfoList = monitorAccessInfoRepository.findByOrderId(re.getOrderId());
            List<String> serialNoList =new ArrayList<>();
            if (!monitorAccessInfoList.isEmpty()){
                monitorAccessInfoList.forEach(info->serialNoList.add(info.getSerialNo()));
            }
            dto.setSerialNo(String.join(",",serialNoList));


        }else if (flow.equals(MonitorFlowEnum.TYPE5.getType())){
            //施工完成， 没有关联的详情

        }else if (flow.equals(MonitorFlowEnum.TYPE6.getType())){
            //回收预约
            if (relateId!=null){
                MonitorRecycleReservationRespDTO entity = monitorRecycleReservationService.show(scShowDTO);
                if (entity!=null){
                    dto.setOrderId(entity.getOrderId());
                    dto.setContactMobile(entity.getContactMobile());
                    dto.setContactName(entity.getContactName());
                    dto.setReservationTime(entity.getReservationTime());
                }
            }


        }else if (flow.equals(MonitorFlowEnum.TYPE7.getType())){
            //上门回收
            if (relateId!=null){
                MonitorRecycleRespDTO entity = monitorRecycleService.show(scShowDTO);
                if (entity!=null){
                    dto.setOrderId(entity.getOrderId());
                    dto.setContactMobile(entity.getContactMobile());
                    dto.setContactName(entity.getContactName());
                    dto.setRecycleTime(entity.getRecycleTime());
                    dto.setRecycleCnt(entity.getRecycleCnt());
                    dto.setPic(entity.getPic());
                    dto.setMemo(entity.getMemo());
                }
            }


        }else if (flow.equals(MonitorFlowEnum.TYPE9.getType())){
            //监管结束,没有内容需要填充
        }
    }

    @Override
    public MonitorFlowRespDTO show(ScShowDTO bean) {
        Optional<MonitorFlow> option = monitorFlowRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorFlow entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorFlow.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorFlowRespDTO.class, this::delData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorFlow create(MonitorFlowCreateDTO bean) {
        MonitorFlow entity = new MonitorFlow();
        BeanUtil.copyProperties(bean, entity);
        return monitorFlowRepository.save(entity);
    }



    /**
     * 初始化监管流程节点
     */
    @Transactional
    public void initFlow(MonitorOrder monitorOrder){
        String orderId = monitorOrder.getId();

        Date now = new Date();

        //节点- 创建工单
        MonitorFlow monitorFlow = new MonitorFlow();
        monitorFlow.setOrderId(orderId);
        monitorFlow.setProjectId(monitorOrder.getProjectId());
        monitorFlow.setState(1);
        monitorFlow.setFlow(MonitorFlowEnum.TYPE0.getType());
        monitorFlow.setFinishTime(now);
        monitorFlow.setCreatedUser(monitorOrder.getCreatedUser());
        monitorFlowRepository.save(monitorFlow);

        //节点 - 预约安装
        MonitorFlow monitorFlow2 = new MonitorFlow();
        monitorFlow2.setOrderId(orderId);
        monitorFlow2.setFlow(MonitorFlowEnum.TYPE1.getType());
        monitorFlow2.setProjectId(monitorOrder.getProjectId());
        MonitorFlow flow = monitorFlowRepository.save(monitorFlow2);


        flow.setMessageType(MessageRemindEnum.TYPE1.getType());
        //事务提交后，发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                sendMessageRemind(flow,new MonitorFlowUpdateDTO());
            }
        });

        //设置工单当前流程节点
        monitorOrder.setFlow(MonitorFlowEnum.TYPE1.getType());
        monitorOrder.setFlowId(flow.getId());
        monitorOrder.setSort(0);
    }


    /**
     * 完成当前流程
     * @param bean 修改DTO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    @Klock(keys = {"#bean.id"})
    public MonitorFlow update(MonitorFlowUpdateDTO bean) {


        //查询当前用户
        SecurityUser currentUser = ScContext.getCurrentUser();
        String userId = null;
        if (currentUser!=null){
            userId=currentUser.getId();
        }


        MonitorFlow entity = monitorFlowRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));

        //查询工单
        MonitorOrder monitorOrder = monitorOrderRepository.findById(entity.getOrderId()).orElseThrow(() -> new ScException("当前工单不存在！"));

        //todo 20250704需要校验当前用户的身份和角色

        //流程节点不允许更改
        bean.setOrderId(entity.getOrderId());
        bean.setFlow(entity.getFlow());
        bean.setProjectId(entity.getProjectId());

        //检查当前节点是否已经完结
        checkIfComplete(entity);

        //完成当前节点
        entity.setState(1);
        entity.setFinishTime(new Date());
        //操作人
        entity.setCreatedUser(userId);


        //填充详细信息,并返回关联id
        String relateId = fillDetail(bean);
        entity.setRelateId(relateId);

        //创建下一个节点
        MonitorFlow monitorFlow = nextFlow(entity);
        //设置工单当前流程节点
        monitorOrder.setFlow(monitorFlow.getFlow());
        monitorOrder.setFlowId(monitorFlow.getId());
        //设置工单自定义排序字段
        setOrderSort(monitorOrder);

        //事务提交后，发送消息
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                sendMessageRemind(monitorFlow,bean);
            }
        });

        //保存
        return monitorFlowRepository.save(entity);
    }



    private void  sendMessageRemind(MonitorFlow nextFlow,MonitorFlowUpdateDTO bean){

        Integer messageType = nextFlow.getMessageType();

        if (messageType!=null){
            XsgcMessageRemindDTO remindDTO = new XsgcMessageRemindDTO();
            remindDTO.setFlowId(nextFlow.getId());
            remindDTO.setReservationTime(bean.getReservationTime());
            remindDTO.setType(messageType);
            remindDTO.setProjectId(nextFlow.getProjectId());
            xsgcMessageRemindSender.send(remindDTO);
        }

    }


    private void setOrderSort(MonitorOrder monitorOrder){

        if (monitorOrder.getFlow().equals(MonitorFlowEnum.TYPE6.getType())){
            monitorOrder.setSort(1);
        }else if (monitorOrder.getFlow().equals(MonitorFlowEnum.TYPE8.getType())){
            monitorOrder.setSort(3);
        }else {
            monitorOrder.setSort(2);
        }

    }


    /**
     * 检查当前流程是否已经结束
     */
    private void checkIfComplete(MonitorFlow entity){

        Integer flow = entity.getFlow();
        if (entity.getState()==1){
            if (flow.equals(MonitorFlowEnum.TYPE1.getType())){
                throw new ScException("该工程已预约监控安装服务，无需重复预约。");
            }
            if (flow.equals(MonitorFlowEnum.TYPE6.getType())){
                throw new ScException("该工程已预约监控回收服务，无需重复预约。");
            }
            throw new ScException("当前流程节点已结束，无法更新！");
        }
    }



    private String fillDetail(MonitorFlowUpdateDTO bean){
        String relateId = null;
        Integer flow = bean.getFlow();
        if (flow.equals(MonitorFlowEnum.TYPE1.getType())){
            //预约回收
            MonitorInstallReservationCreateDTO createDTO = new MonitorInstallReservationCreateDTO();
            createDTO.setOrderId(bean.getOrderId());
            createDTO.setContactMobile(bean.getContactMobile());
            createDTO.setContactName(bean.getContactName());
            createDTO.setReservationTime(bean.getReservationTime());
            MonitorInstallReservation reservation = monitorInstallReservationService.create(createDTO);
            relateId = reservation.getId();
        }else if (flow.equals(MonitorFlowEnum.TYPE2.getType())){
            //现场勘察
            MonitorOnSceneInspectionCreateDTO createDTO = new MonitorOnSceneInspectionCreateDTO();
            createDTO.setOrderId(bean.getOrderId());
            createDTO.setMemo(bean.getMemo());
            createDTO.setPic(bean.getPic());
            createDTO.setContactName(bean.getContactName());
            createDTO.setContactMobile(bean.getContactMobile());
            createDTO.setInspectTime(bean.getInspectTime());
            createDTO.setReservationTime(bean.getReservationTime());
            createDTO.setInstallCnt(bean.getInstallCnt());
            MonitorOnSceneInspection onSceneInspection = monitorOnSceneInspectionService.create(createDTO);
            relateId = onSceneInspection.getId();
        }else if (flow.equals(MonitorFlowEnum.TYPE3.getType())){
            //上门安装
            MonitorInstallCreateDTO createDTO = new MonitorInstallCreateDTO();
            createDTO.setOrderId(bean.getOrderId());
            createDTO.setMemo(bean.getMemo());
            createDTO.setPic(bean.getPic());
            createDTO.setInstallTime(bean.getInstallTime());
            createDTO.setInstallCnt(bean.getInstallCnt());
            MonitorInstall monitorInstall = monitorInstallService.create(createDTO);
            relateId= monitorInstall.getId();
        }else if (flow.equals(MonitorFlowEnum.TYPE4.getType())){
            //接入监管， 在配置摄像头时进行

        }else if (flow.equals(MonitorFlowEnum.TYPE5.getType())){
            //施工完成， 由定时任务处理

        }else if (flow.equals(MonitorFlowEnum.TYPE6.getType())){
            //回收预约
            MonitorRecycleReservationCreateDTO createDTO = new MonitorRecycleReservationCreateDTO();
            createDTO.setOrderId(bean.getOrderId());
            createDTO.setContactMobile(bean.getContactMobile());
            createDTO.setContactName(bean.getContactName());
            createDTO.setReservationTime(bean.getReservationTime());
            MonitorRecycleReservation recycleReservation = monitorRecycleReservationService.create(createDTO);
            relateId = recycleReservation.getId();
        }else if (flow.equals(MonitorFlowEnum.TYPE7.getType())){
            //上门回收
            MonitorRecycleCreateDTO createDTO = new MonitorRecycleCreateDTO();
            createDTO.setOrderId(bean.getOrderId());
            createDTO.setRecycleTime(bean.getRecycleTime());
            createDTO.setMemo(bean.getMemo());
            createDTO.setRecycleCnt(bean.getRecycleCnt());
            createDTO.setRecycleTime(bean.getRecycleTime());
            createDTO.setPic(bean.getPic());
            MonitorRecycle monitorRecycle = monitorRecycleService.create(createDTO);
            relateId = monitorRecycle.getId();
        }
        else if (flow.equals(MonitorFlowEnum.TYPE8.getType())){
            //等待移除监控设备

        }else if (flow.equals(MonitorFlowEnum.TYPE9.getType())){
            //监管结束

        }
        return relateId;
    }


    /**
     * 创建下一个节点
     * @param currentFlow
     * @return
     */
    @Transactional
    public MonitorFlow nextFlow(MonitorFlow currentFlow){
        Integer flow = currentFlow.getFlow();

        MonitorFlow nextFlow = new MonitorFlow();
        nextFlow.setOrderId(currentFlow.getOrderId());
        nextFlow.setProjectId(currentFlow.getProjectId());

        if (flow.equals(MonitorFlowEnum.TYPE1.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE2.getType());
            nextFlow.setMessageType(MessageRemindEnum.TYPE3.getType());
        }
        else if (flow.equals(MonitorFlowEnum.TYPE2.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE3.getType());
            nextFlow.setMessageType(MessageRemindEnum.TYPE4.getType());
        }
        else if (flow.equals(MonitorFlowEnum.TYPE3.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE4.getType());

        }
        else if (flow.equals(MonitorFlowEnum.TYPE4.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE5.getType());
            nextFlow.setMessageType(MessageRemindEnum.TYPE6.getType());

        }
        else if (flow.equals(MonitorFlowEnum.TYPE5.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE6.getType());

            nextFlow.setMessageType(MessageRemindEnum.TYPE2.getType());
        }
        else if (flow.equals(MonitorFlowEnum.TYPE6.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE7.getType());

            nextFlow.setMessageType(MessageRemindEnum.TYPE5.getType());

        }else if (flow.equals(MonitorFlowEnum.TYPE7.getType())){
            nextFlow.setFlow(MonitorFlowEnum.TYPE8.getType());

        }else if (flow.equals(MonitorFlowEnum.TYPE8.getType())){

            //完成了 移除监控设备之后， 监管结束节点直接完成
            nextFlow.setState(1);
            nextFlow.setFinishTime(new Date());
            nextFlow.setFlow(MonitorFlowEnum.TYPE9.getType());

            nextFlow.setMessageType(MessageRemindEnum.TYPE7.getType());
        }

        return monitorFlowRepository.save(nextFlow);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorFlowRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                monitorFlowRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(MonitorFlowQueryDTO bean) {
        List<MonitorFlowRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorFlowRespDTO.class);
    }


    @Override
    public MonitorFlow findByOrderIdAndFlow(String orderId, Integer flow) {
        return monitorFlowRepository.findByOrderIdAndFlow(orderId,flow);
    }


    @Override
    public List<String> findOwnerOrderIds(String userId, Integer flow) {
        return monitorFlowRepository.findOrderIdsByCreatedUserAndFlow(userId,flow);
    }
}