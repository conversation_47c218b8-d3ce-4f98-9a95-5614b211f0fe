package cn.shencom.server.service.impl;

import cn.shencom.model.dto.query.ViolationTrendQueryDTO;
import cn.shencom.model.dto.resp.ReportRespDTO.ProjectStatisticsRespDTO;
import cn.shencom.model.dto.resp.SporadicProjectRespDTO;
import cn.shencom.model.dto.resp.ViolationTrendRespDTO;
import cn.shencom.repos.ContractingUnitOrganizationRelateRepository;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.server.service.IReportService;
import cn.shencom.server.service.ISporadicProjectService;
import cn.shencom.utils.XsgcContext;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 统计报表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
@Slf4j
public class ReportServiceImpl extends BaseImpl implements IReportService {

    @PersistenceContext
    private EntityManager entityManager;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Autowired
    private ContractingUnitOrganizationRelateRepository contractingUnitOrganizationRelateRepository;

    @Override
    public ProjectStatisticsRespDTO getProjectStatistics() {
        log.info("开始获取统计数据");

        // 1. 统计施工单位数量
        Integer contractingUnitCount = countContractingUnits();

        // 2. 统计工程总数
        Integer totalProjectCount = 0;

        // 3. 统计施工中工程数量
        Integer ongoingProjectCount = 0;

        // 4. 统计工程总金额
        BigDecimal totalAmount = BigDecimal.ZERO;

        List<SporadicProjectRespDTO> projectList = sporadicProjectService.getUserProjectList();

        totalProjectCount = projectList.size();

        ongoingProjectCount = (int) projectList.stream().filter(project -> project.getStatus() == 1).count();

        totalAmount = projectList.stream().map(SporadicProjectRespDTO::getAmount).reduce(BigDecimal.ZERO,
                BigDecimal::add);

        return ProjectStatisticsRespDTO.builder()
                .contractingUnitCount(contractingUnitCount)
                .totalProjectCount(totalProjectCount)
                .ongoingProjectCount(ongoingProjectCount)
                .totalAmount(totalAmount)
                .build();
    };

    /**
     * 统计施工单位数量
     *
     * @return 施工单位数量
     */
    private Integer countContractingUnits() {
        String organizationId = XsgcContext.getOrganizationId();

        Integer count = contractingUnitOrganizationRelateRepository
                .findDistinctContractingUnitIdByOrganizationIdCount(organizationId);

        if (count == null) {
            return 0;
        }

        return count;
    }

}
