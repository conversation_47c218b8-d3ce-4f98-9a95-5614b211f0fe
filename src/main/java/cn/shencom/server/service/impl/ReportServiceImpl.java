package cn.shencom.server.service.impl;

import cn.shencom.model.dto.resp.StatisticsRespDTO;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.server.service.IReportService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 统计报表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Service
@Slf4j
public class ReportServiceImpl extends BaseImpl implements IReportService {

    @PersistenceContext
    private EntityManager entityManager;

    @Override
    public StatisticsRespDTO getStatistics() {
        log.info("开始获取统计数据");

        // 1. 统计施工单位数量
        Long contractingUnitCount = countContractingUnits();

        // 2. 统计工程总数
        Long totalProjectCount = countTotalProjects();

        // 3. 统计施工中工程数量
        Long ongoingProjectCount = countOngoingProjects();

        // 4. 统计工程总金额
        BigDecimal totalAmount = calculateTotalAmount();

        log.info("统计数据获取完成: 施工单位数量={}, 工程总数={}, 施工中工程数量={}, 工程总金额={}万元",
                contractingUnitCount, totalProjectCount, ongoingProjectCount, totalAmount);

        return StatisticsRespDTO.builder()
                .contractingUnitCount(contractingUnitCount)
                .totalProjectCount(totalProjectCount)
                .ongoingProjectCount(ongoingProjectCount)
                .totalAmount(totalAmount)
                .build();
    }

    /**
     * 统计施工单位数量
     *
     * @return 施工单位数量
     */
    private Long countContractingUnits() {
        String sql = "SELECT COUNT(1) FROM contracting_unit WHERE is_deleted = 0";
        Query query = entityManager.createNativeQuery(sql);
        return ((Number) query.getSingleResult()).longValue();
    }

    /**
     * 统计工程总数
     *
     * @return 工程总数
     */
    private Long countTotalProjects() {
        String sql = "SELECT COUNT(1) FROM sporadic_project WHERE is_deleted = 0";
        Query query = entityManager.createNativeQuery(sql);
        return ((Number) query.getSingleResult()).longValue();
    }

    /**
     * 统计施工中工程数量
     *
     * @return 施工中工程数量
     */
    private Long countOngoingProjects() {
        String sql = "SELECT COUNT(1) FROM sporadic_project WHERE is_deleted = 0 AND status = 1";
        Query query = entityManager.createNativeQuery(sql);
        return ((Number) query.getSingleResult()).longValue();
    }

    /**
     * 计算工程总金额（万元）
     *
     * @return 工程总金额（万元）
     */
    private BigDecimal calculateTotalAmount() {
        String sql = "SELECT COALESCE(SUM(amount), 0) FROM sporadic_project WHERE is_deleted = 0";
        Query query = entityManager.createNativeQuery(sql);
        BigDecimal totalAmount = (BigDecimal) query.getSingleResult();
        
        // 转换为万元单位，并保留2位小数
        return totalAmount.divide(new BigDecimal("10000"), 2, RoundingMode.HALF_UP);
    }
}
