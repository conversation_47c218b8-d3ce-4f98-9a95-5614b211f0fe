package cn.shencom.server.service.impl;

import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.MonitorFlow;
import cn.shencom.model.MonitorOrder;
import cn.shencom.model.SporadicProject;
import cn.shencom.model.dto.create.MonitorFlowCreateDTO;
import cn.shencom.model.dto.create.MonitorOrderCreateDTO;
import cn.shencom.model.dto.query.MonitorOrderQueryDTO;
import cn.shencom.model.dto.resp.MonitorFlowRespDTO;
import cn.shencom.model.dto.resp.MonitorOrderRespDTO;
import cn.shencom.model.dto.update.MonitorOrderUpdateDTO;
import cn.shencom.repos.MonitorOrderRepository;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IMonitorFlowService;
import cn.shencom.server.service.IMonitorOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.util.*;


/**
 * 小散工程-监管工单 的服务实现
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Service
@Slf4j
public class MonitorOrderServiceImpl extends BaseImpl implements IMonitorOrderService {

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private IMonitorFlowService monitorFlowService;

    @Override
    public Page<MonitorOrderRespDTO> query(MonitorOrderQueryDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();

        List<String>  orderIds = new ArrayList<>();
        //如果是业主/施工负责人   只能看到自己提交的预约
        if (bean.getQueryType()!=null&&bean.getQueryType()==2){
            if (bean.getInstallType()!=null){
                orderIds = monitorFlowService.findOwnerOrderIds(userId,MonitorFlowEnum.TYPE1.getType() );
                if (orderIds.isEmpty()){
                    return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
                }
            }else if (bean.getRecycleType()!=null){
                orderIds = monitorFlowService.findOwnerOrderIds(userId,MonitorFlowEnum.TYPE6.getType() );
                if (orderIds.isEmpty()){
                    return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
                }
            }
        }


        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorOrder.class);
        List<String> finalOrderIds = orderIds;
        Page<MonitorOrder> res = monitorOrderRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (!finalOrderIds.isEmpty()){
                ps.add(builder.in(root.get("id")).value(finalOrderIds));
            }


            //安装情况查询
            Integer installType = bean.getInstallType();
            if (installType!=null){
                if (installType==1){
                    ps.add(builder.equal(root.get("flow"), MonitorFlowEnum.TYPE2.getType()));
                }else if (installType==2){
                    ps.add(builder.equal(root.get("flow"), MonitorFlowEnum.TYPE3.getType()));
                }else if (installType==3){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE3.getType()));
                }else if (installType ==0 ){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE1.getType()));
                }
            }


            //回收状态查询
            Integer recycleType = bean.getRecycleType();
            if (recycleType!=null){
                if (recycleType==1){
                    ps.add(builder.equal(root.get("flow"), MonitorFlowEnum.TYPE7.getType()));
                }else if (recycleType==2){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE7.getType()));
                }else if (recycleType ==0 ){
                    ps.add(builder.greaterThan(root.get("flow"), MonitorFlowEnum.TYPE6.getType()));
                }
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        return ScQueryUtil.handle(res, linkMap, MonitorOrderRespDTO.class,this::delData);
    }



    private void delData(MonitorOrder re,MonitorOrderRespDTO dto){
        Integer flow = re.getFlow();
        if (flow.equals(MonitorFlowEnum.TYPE2.getType())){
            dto.setInstallStatus(1);
            //查询预约时间
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(re.getId(), MonitorFlowEnum.TYPE1.getType());
            if (monitorFlow!=null){
                ScShowDTO scShowDTO = new ScShowDTO();
                scShowDTO.setId(monitorFlow.getId());
                MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
                if (flowRespDTO!=null){
                    dto.setReservationTime(flowRespDTO.getReservationTime());
                }
            }


        }else if (flow.equals(MonitorFlowEnum.TYPE3.getType())){

            //查询预约时间
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(re.getId(), MonitorFlowEnum.TYPE2.getType());
            if (monitorFlow!=null){
                ScShowDTO scShowDTO = new ScShowDTO();
                scShowDTO.setId(monitorFlow.getId());
                MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
                if (flowRespDTO!=null){
                    dto.setReservationTime(flowRespDTO.getReservationTime());
                }
            }


            dto.setInstallStatus(2);
        }else if (flow>MonitorFlowEnum.TYPE3.getType()){
            dto.setInstallStatus(3);
        }

        if (flow.equals(MonitorFlowEnum.TYPE7.getType())){
            //查询预约时间
            MonitorFlow monitorFlow = monitorFlowService.findByOrderIdAndFlow(re.getId(), MonitorFlowEnum.TYPE6.getType());
            if (monitorFlow!=null){
                ScShowDTO scShowDTO = new ScShowDTO();
                scShowDTO.setId(monitorFlow.getId());
                MonitorFlowRespDTO flowRespDTO = monitorFlowService.show(scShowDTO);
                if (flowRespDTO!=null){
                    dto.setReservationTime(flowRespDTO.getReservationTime());
                }
            }

            dto.setRecycleStatus(1);
        }else if (flow>MonitorFlowEnum.TYPE7.getType()){
            dto.setRecycleStatus(2);
        }

    }



    @Override
    public MonitorOrderRespDTO show(ScShowDTO bean) {
        Optional<MonitorOrder> option = monitorOrderRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        MonitorOrder entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(MonitorOrder.class);
        return ScQueryUtil.handleOne(entity, linkMap, MonitorOrderRespDTO.class,this::delData);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorOrder create(MonitorOrderCreateDTO bean) {
        MonitorOrder entity = new MonitorOrder();
        BeanUtil.copyProperties(bean, entity);


        MonitorOrder save = monitorOrderRepository.save(entity);

        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public MonitorOrder update(MonitorOrderUpdateDTO bean) {
        MonitorOrder entity = monitorOrderRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        return monitorOrderRepository.save(entity);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> monitorOrderRepository.findById(id).ifPresent(entity -> {
                entity.setIsDeleted(1);
                monitorOrderRepository.save(entity);
            }));
        }
    }

    @Override
    public void export(MonitorOrderQueryDTO bean) {
        List<MonitorOrderRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, MonitorOrderRespDTO.class);
    }


    @Override
    @Transactional
    public void createOrder(SporadicProject sporadicProject) {
        MonitorOrderCreateDTO createDTO = new MonitorOrderCreateDTO();
        //填充属性
        createDTO.setOrganizationId(sporadicProject.getOrganizationId());
        createDTO.setCreatedUser(sporadicProject.getCreateUser());
        createDTO.setProjectId(sporadicProject.getId());
        createDTO.setRegionPid(sporadicProject.getRegionPid());
        createDTO.setRegionId(sporadicProject.getRegionId());
        createDTO.setRegionCid(sporadicProject.getRegionCid());
        MonitorOrder order = create(createDTO);

        monitorFlowService.initFlow(order);
    }


    @Override
    public MonitorOrder findByProjectId(String projectId) {
        return monitorOrderRepository.findByProjectId(projectId);
    }
}