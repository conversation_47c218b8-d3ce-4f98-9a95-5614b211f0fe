package cn.shencom.server.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.shencom.enums.MonitorFlowEnum;
import cn.shencom.model.*;
import cn.shencom.model.dto.create.EventCameraPointCreateDTO;
import cn.shencom.model.dto.create.EventCameraPointDeviceCreateDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceQueryDTO;
import cn.shencom.model.dto.query.EventCameraPointDeviceMobileQueryDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceRespDTO;
import cn.shencom.model.dto.resp.EventCameraPointDeviceMobileRespDTO;
import cn.shencom.model.dto.update.EventCameraPointDeviceUpdateDTO;
import cn.shencom.model.dto.update.EventCameraPointUpdateDTO;
import cn.shencom.model.dto.update.MonitorFlowUpdateDTO;
import cn.shencom.repos.*;
import cn.shencom.scloud.common.base.constant.RespCode;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.dto.ScShowDTO;
import cn.shencom.scloud.common.jpa.scpage.MyLink;
import cn.shencom.scloud.common.jpa.util.export.ScExport;
import cn.shencom.scloud.common.jpa.util.query.LinkUtil;
import cn.shencom.scloud.common.jpa.util.query.ScQueryUtil;
import cn.shencom.scloud.common.server.service.BaseImpl;
import cn.shencom.scloud.common.util.BeanUtil;
import cn.shencom.scloud.common.util.LambdaBeanUtil;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.scloud.security.core.SecurityUser;
import cn.shencom.server.service.IEventCameraPointDeviceService;
import cn.shencom.server.service.IEventCameraPointService;
import cn.shencom.server.service.IMonitorAccessInfoService;
import cn.shencom.server.service.IMonitorFlowService;
import cn.shencom.server.service.ISporadicProjectService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Join;
import javax.persistence.criteria.JoinType;
import javax.persistence.criteria.Predicate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 摄像头表 的服务实现
 *
 * <AUTHOR>
 * @since 2025-05-16
 */
@Service
@Slf4j
public class EventCameraPointDeviceServiceImpl extends BaseImpl implements IEventCameraPointDeviceService {

    @Autowired
    private EventCameraPointDeviceRepository eventCameraPointDeviceRepository;

    @Autowired
    private IEventCameraPointService eventCameraPointService;

    @Autowired
    private EventCameraPointRepository eventCameraPointRepository;

    @Autowired
    private SporadicProjectRepository sporadicProjectRepository;

    @Autowired
    private MonitorOrderRepository monitorOrderRepository;

    @Autowired
    private MonitorFlowRepository monitorFlowRepository;

    @Autowired
    private IMonitorFlowService monitorFlowService;

    @Autowired
    private IMonitorAccessInfoService monitorAccessInfoService;

    @Autowired
    private ISporadicProjectService sporadicProjectService;

    @Override
    public Page<EventCameraPointDeviceRespDTO> query(EventCameraPointDeviceQueryDTO bean) {
        ScQueryUtil.getValuesRmAndSetBean(bean.getQuery(), Arrays.asList("realStatus", "status", "isHealthy", "modelNo", "pointExist"), bean);
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventCameraPointDevice.class);
        Page<EventCameraPointDevice> res = eventCameraPointDeviceRepository.findAll((root, query, builder) -> {
            //用于拼接条件
            List<Predicate> ps = new ArrayList<>();

            if (Objects.nonNull(bean.getRealStatus())
                    || Objects.nonNull(bean.getStatus())
                    ||Objects.nonNull(bean.getIsHealthy())
                    ||StrUtil.isNotBlank(bean.getModelNo())) {
                Join<Object, Object> eventCameraPointList = root.join("eventCameraPointList", JoinType.LEFT);
                if(Objects.nonNull(bean.getRealStatus())){
                    ps.add(builder.equal(eventCameraPointList.get("realStatus"), bean.getRealStatus()));
                }
                if(Objects.nonNull(bean.getStatus())){
                    ps.add(builder.equal(eventCameraPointList.get("status"), bean.getStatus()));
                }
                if(Objects.nonNull(bean.getIsHealthy())){
                    ps.add(builder.equal(eventCameraPointList.get("isHealthy"), bean.getIsHealthy()));
                }
                if(Objects.nonNull(bean.getModelNo())){
                    ps.add(builder.equal(eventCameraPointList.get("modelNo"), bean.getModelNo()));
                }
            }

            return ScQueryUtil.getPredicate(ps, bean, linkMap, root, query, builder);
        }, PageRequest.of(bean.getPage(), bean.getSize()));
        Map<String, MyLink> linkMapCamera = LinkUtil.convertLink(EventCameraPoint.class);
        return ScQueryUtil.handle(res, linkMap, EventCameraPointDeviceRespDTO.class, (r, dto) -> {
            if (CollectionUtils.isNotEmpty(r.getEventCameraPointList())) {
                ScQueryUtil.dealWith(r.getEventCameraPointList(), linkMapCamera);
            }
        });
    }

    @Override
    public EventCameraPointDeviceRespDTO show(ScShowDTO bean) {
        Optional<EventCameraPointDevice> option = eventCameraPointDeviceRepository.findById(bean.getId());
        if (!option.isPresent()) {
            return null;
        }
        EventCameraPointDevice entity = option.get();
        Map<String, MyLink> linkMap = LinkUtil.convertLink(EventCameraPointDevice.class);
        Map<String, MyLink> linkMapCamera = LinkUtil.convertLink(EventCameraPoint.class);
        return ScQueryUtil.handleOne(entity, linkMap, EventCameraPointDeviceRespDTO.class,(r,dto)->{
            if (CollectionUtils.isNotEmpty(r.getEventCameraPointList())) {
                ScQueryUtil.dealWith(r.getEventCameraPointList(), linkMapCamera);
            }
        });
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventCameraPointDevice create(EventCameraPointDeviceCreateDTO bean) {


        String userId = ScContext.getCurrentUserThrow().getId();


        EventCameraPointDevice entity = new EventCameraPointDevice();
        // 提交的
        String oSerialNo = bean.getSerialNo();
        BeanUtil.copyProperties(bean, entity);
        EventCameraPointDevice cameraPointDevice = eventCameraPointDeviceRepository.findFirstBySerialNo(bean.getSerialNo());
        if (Objects.nonNull(cameraPointDevice)) {
            throw new ScException(StrUtil.format("序列号{}已存在，无法新增,对应12位序列号:{}",oSerialNo,entity.getSerialNo()));
        }

        if (bean.getType()==22){
            if (StringUtils.isBlank(bean.getSipUserId())){
                throw new ScException("sip不能为空！");
            }
            //判断sip不能重复
            EventCameraPointDevice firstDeviceBySipUserId = eventCameraPointDeviceRepository.findFirstBySipUserId(bean.getSipUserId());
            if (Objects.nonNull(firstDeviceBySipUserId)){
                throw new ScException(StrUtil.format("sip{}已存在，无法新增,对应12位序列号:{}",firstDeviceBySipUserId.getSerialNo()));
            }
        }

        if (CollectionUtils.isEmpty(bean.getChannelList())) {
            throw new ScException("通道列表不能为空");
        }
        EventCameraPointDevice pointDevice = eventCameraPointDeviceRepository.save(entity);
        bean.getChannelList().forEach(channel -> {
            BeanUtil.copyProperties(bean, channel);
            channel.setDeviceId(pointDevice.getId());
            eventCameraPointService.create(pointDevice, channel);
        });


        //处理监管流程
        processMonitorOrder(pointDevice.getProjectId());


        return pointDevice;
    }


    /**
     * 处理监管流程
     */
    private void  processMonitorOrder(String projectId){

        //查询当前工程监管流程工单
        MonitorOrder monitorOrder = monitorOrderRepository.findByProjectId(projectId);
        if (monitorOrder==null){
            throw new ScException("当前项目监管流程工单不存在！");
        }

        //判断当前项目的流程节点
        MonitorFlow monitorFlow = monitorFlowRepository.findByOrderIdAndFlow(monitorOrder.getId(), monitorOrder.getFlow());
        if (monitorOrder.getFlow().equals( MonitorFlowEnum.TYPE4.getType())){
            //如果当前正处于接入监管环节，则需要往下走流程，完结接入监管的流程节点
            MonitorFlowUpdateDTO flowUpdateDTO = new MonitorFlowUpdateDTO();
            flowUpdateDTO.setId(monitorFlow.getId());
            flowUpdateDTO.setState(1);
            //创建下一个流程节点
            monitorFlowService.update(flowUpdateDTO);

            //查询项目信息
            SporadicProject sporadicProject = sporadicProjectRepository.findById(projectId).get();
            //已接入监管
            sporadicProject.setMonitorFlag(1);
            sporadicProjectRepository.save(sporadicProject);
        }

        //如果不是“移除监管”的环节，则需要更新关联的监管信息
        if (!monitorFlow.getFlow().equals(MonitorFlowEnum.TYPE8.getType())){
            //修改接入监管的信息
            monitorAccessInfoService.createOrUpdateInfo(monitorOrder.getProjectId(), monitorOrder.getId());
        }

    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public EventCameraPointDevice update(EventCameraPointDeviceUpdateDTO bean) {

        String userId = ScContext.getCurrentUserThrow().getId();

        EventCameraPointDevice entity = eventCameraPointDeviceRepository.findById(bean.getId())
                .orElseThrow(() -> new ScException(RespCode.NOT_FIND_RECORD));
        BeanUtil.copyProperties(bean, entity);
        if (CollectionUtils.isEmpty(bean.getChannelList())) {
            throw new ScException("通道列表不能为空");
        }
        entity.setSipUserId(StringUtils.isBlank(bean.getSipUserId()) ? null : bean.getSipUserId());
        List<String> channelIdList = new ArrayList<>();
        // 找到对应的id
        bean.getChannelList().forEach(channel -> {
            LambdaBeanUtil.copyProperties(bean, channel, EventCameraPointUpdateDTO::getId);
            if (StringUtils.isBlank(channel.getId())) {
                channel.setDeviceId(bean.getId());
                EventCameraPointCreateDTO createDTO = new EventCameraPointCreateDTO();
                LambdaBeanUtil.copyProperties(channel, createDTO);
                String cameraId = eventCameraPointService.create(entity, createDTO);
                channelIdList.add(cameraId);
            } else {
                Optional<EventCameraPoint> pointOptional = eventCameraPointRepository.findById(channel.getId());
                if (pointOptional.isPresent()) {
                    eventCameraPointService.update(entity, channel);
                    channelIdList.add(channel.getId());
                }
            }
        });
        // 把没用到的删掉
        eventCameraPointRepository.deleteNoContainCamera(bean.getId(), channelIdList);

        EventCameraPointDevice save = eventCameraPointDeviceRepository.save(entity);

        //处理监控管理流程
        processMonitorOrder(save.getProjectId());

        return save;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(List<String> ids) {

        if (CollectionUtils.isNotEmpty(ids)) {
            ids.forEach(id -> eventCameraPointDeviceRepository.findById(id).ifPresent(entity -> {
                String projectId = entity.getProjectId();
                entity.setIsDeleted(1);
                eventCameraPointDeviceRepository.save(entity);

                List<String> channelIdList = new ArrayList<>();
                eventCameraPointRepository.deleteByDeviceId(id);

                //查询当前工单流程，如果不是处于结束监管状态，就需要更新关联关系
                processMonitorOrder(projectId);
            }));
        }
    }

    @Override
    public void export(EventCameraPointDeviceQueryDTO bean) {
        List<EventCameraPointDeviceRespDTO> dtos = new ArrayList<>(query(bean).getContent());
        ScExport.export(bean.getCfg(), dtos, EventCameraPointDeviceRespDTO.class);
    }

    @Override
    public Page<EventCameraPointDeviceMobileRespDTO> mobileIndex(EventCameraPointDeviceMobileQueryDTO bean) {
        // 获取用户信息
        SecurityUser currentUser = ScContext.getCurrentUser();
        if (currentUser == null) {
            throw new ScException(RespCode.CURRENT_USER_NOT_EXIST);
        }

        List<String> projectIdList = sporadicProjectService.mobileSelect().stream().map(r -> r.getId())
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(projectIdList)) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        if (StringUtils.isNotBlank(bean.getProjectId()) && !projectIdList.contains(bean.getProjectId())) {
            return Page.empty(PageRequest.of(bean.getPage(), bean.getSize()));
        }

        if (StringUtils.isNotBlank(bean.getProjectId())) {
            bean.setProjectIds(Arrays.asList(bean.getProjectId()));
        }else{
            bean.setProjectIds(projectIdList);
        }

        PageRequest pageable = PageRequest.of(bean.getPage(), bean.getSize());

        Page<EventCameraPointDeviceMobileRespDTO> page = eventCameraPointDeviceRepository.mobileIndex(bean, pageable);

        List<String> deviceIds = page.getContent().stream().map(r -> r.getId()).collect(Collectors.toList());

        List<EventCameraPoint> cameraList = eventCameraPointRepository.findByDeviceIdIn(deviceIds);

        Page<EventCameraPointDeviceMobileRespDTO> res = ScQueryUtil.handle(page, null,
                EventCameraPointDeviceMobileRespDTO.class,
                (r, dto) -> {
                    List<EventCameraPoint> cameras = cameraList.stream()
                            .filter(r1 -> r1.getDeviceId().equals(r.getId()))
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(cameras)) {
                        dto.setEventCameraPointList(cameras);
                    }
                });

        return res;
    }

}