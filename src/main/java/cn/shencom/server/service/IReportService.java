package cn.shencom.server.service;

import cn.shencom.model.dto.query.ViolationTrendQueryDTO;
import cn.shencom.model.dto.resp.ReportRespDTO.ProjectStatisticsRespDTO;
import cn.shencom.model.dto.resp.ViolationTrendRespDTO;

import java.util.List;

/**
 * 统计报表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public interface IReportService {

    /**
     * 获取工程统计数据
     * 包括：施工单位数量、工程总数、施工中工程数量、工程总金额
     *
     * @return 统计数据DTO
     */
    ProjectStatisticsRespDTO getProjectStatistics();

    /**
     * 获取违规告警趋势统计
     * 基于 aiot_event_statistics 表进行统计，按日期排序返回每天的违规告警数量
     *
     * @param queryDTO 查询条件，包含开始日期和结束日期
     * @return 违规告警趋势数据列表，按日期排序
     */
    List<ViolationTrendRespDTO> getViolationTrend(ViolationTrendQueryDTO queryDTO);
}
