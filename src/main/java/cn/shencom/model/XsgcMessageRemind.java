package cn.shencom.model;

import cn.shencom.scloud.common.base.snowflake.SnowflakeGenerator;
import lombok.*;
import org.hibernate.annotations.DynamicInsert;
import org.hibernate.annotations.DynamicUpdate;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.Where;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;

/**
 * 小散工程-消息提醒表
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Entity
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@DynamicUpdate
@DynamicInsert
@Table(name = "xsgc_message_remind")
@Where(clause = "is_deleted = 0")
@EntityListeners(AuditingEntityListener.class)
public class XsgcMessageRemind implements Serializable {
//===========================数据库字段================================
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GenericGenerator(name = "snowflake", strategy = SnowflakeGenerator.TYPE)
    @GeneratedValue(generator = "snowflake")
    private String id;

    /**
     * 工程id
     */
    @Column(name = "project_id")
    private String projectId;

    /**
     * 消息类型，1-预约安装提醒，2-预约回收提醒，3-现场勘察提醒，4-上门安装提醒，5-上门回收提醒，6-已接入监管的提醒，7-结束监管的提醒，8-违规告警的提醒
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 关联id，1~7是关联monitor_flow的id, 8是关联aiot_event的id
     */
    @Column(name = "relate_id")
    private String relateId;

    /**
     * 是否已读,0-未读，1-已读
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 消息内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 备注信息，json字符串格式
     */
    @Column(name = "memo")
    private String memo;

    /**
     * 创建时间
     */
    @Column(name = "created_at")
    @CreatedDate
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Column(name = "updated_at")
    @LastModifiedDate
    private java.util.Date updatedAt;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    private String userId;

    /**
     * 是否删除
     */
    @Column(name = "is_deleted")
    private Integer isDeleted;

    /**
     * 删除时间
     */
    @Column(name = "deleted_at")
    private java.util.Date deletedAt;

//=============================表关联==================================


//===========================自定义字段=================================

}
