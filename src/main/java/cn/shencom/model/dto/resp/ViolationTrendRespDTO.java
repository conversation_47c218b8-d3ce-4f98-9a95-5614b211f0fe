package cn.shencom.model.dto.resp;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 违规告警趋势统计响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ViolationTrendRespDTO implements Serializable {

    /**
     * 日期
     */
    private Date date;

    /**
     * 当日违规告警数量
     */
    private Long data;
}
