package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorOrder
 * @since 2025-07-04
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("MonitorOrderRespDTO")
public class MonitorOrderRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 项目id
     */
    @Excel(name = "项目id", width = 25)
    private String projectId;

    /**
     * 组织id
     */
    @Excel(name = "组织id", width = 25)
    private String organizationId;

    /**
     * 区id
     */
    @Excel(name = "区id", width = 25)
    private String regionPid;

    /**
     * 街道id
     */
    @Excel(name = "街道id", width = 25)
    private String regionId;

    /**
     * 社区id
     */
    @Excel(name = "社区id", width = 25)
    private String regionCid;

    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束
     */
    @Excel(name = "当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束", width = 25)
    private Integer flow;



    /**
     * 流程id
     */
    @Excel(name = "流程id", width = 25)
    private String flowId;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUser;

    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;


    /**
     * 项目名称
     */
    @Excel(name = "项目名称", width = 25)
    private String projectName;


    /**
     * 项目地址
     */
    @Excel(name = "项目地址", width = 25)
    private String projectAddress;


    /**
     * 项目编号
     */
    private String projectNumber;


    /**
     * 点位id
     */
    private String projectPoiId;


    /**
     * 安装状态， 1-未勘察，2-未安装，3-已完成
     */
    private Integer installStatus;


    /**
     * 回收状态， 1-待回收，2-已完成
     */
    private Integer recycleStatus;


    /**
     * 预约时间
     */
    private java.util.Date reservationTime;

}
