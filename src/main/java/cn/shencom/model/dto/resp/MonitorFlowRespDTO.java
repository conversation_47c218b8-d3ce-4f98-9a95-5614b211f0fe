package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorFlow
 * @since 2025-07-05
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("MonitorFlowRespDTO")
public class MonitorFlowRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    @Excel(name = "工单id，关联monitor_order", width = 25)
    private String orderId;

    /**
     * 详情id ，关联
     */
    @Excel(name = "详情id ，关联", width = 25)
    private String relateId;

    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束
     */
    @Excel(name = "当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束", width = 25)
    private Integer flow;

    /**
     * 当前节点状态，0-未完成，1-已完成
     */
    @Excel(name = "当前节点状态，0-未完成，1-已完成", width = 25)
    private Integer state;

    /**
     * 当前节点结束时间
     */
    @Excel(name = "当前节点结束时间", width = 25)
    private java.util.Date finishTime;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    private String createdUser;

    private String createdUserPhone;


    /**
     * 修改人
     */
    @Excel(name = "修改人", width = 25)
    private String updatedUser;



    /**
     * 创建人
     */
    @Excel(name = "创建人", width = 25)
    private String createdUserName;



    //=============额外字段

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人姓名
     */
    private String contactName;



    // 预约安装需要的字段contactMobile，contactMobile，reservationTime
    /**
     * 预约时间
     */
    private java.util.Date reservationTime;


    //现场勘察需要的字段 contactMobile，contactMobile，memo，pic，inspectTime，installCnt

    /**
     * 勘察说明
     */
    private String memo;

    /**
     * 现场图片
     */
    private String pic;

    /**
     * 勘察时间
     */
    private java.util.Date inspectTime;

    /**
     * 评估需要安装的监控数
     */
    private Integer installCnt;


    //上门安装需要的字段  contactMobile，contactMobile，memo，pic，installTime，installCnt

    /**
     * 安装时间
     */
    private java.util.Date installTime;


    //接入监管详情的字段
    private String serialNo;



    //预约回收需要的字段 contactMobile，contactMobile，reservationTime

    //上门回收的字段 recycleTime

    /**
     * 回收时间
     */
    private java.util.Date recycleTime;

    /**
     * 回收摄像头数量
     */
    private Integer recycleCnt;


}
