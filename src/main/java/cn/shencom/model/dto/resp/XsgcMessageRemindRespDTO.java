package cn.shencom.model.dto.resp;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-消息提醒表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.XsgcMessageRemind
 * @since 2025-07-15
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@ExcelTarget("XsgcMessageRemindRespDTO")
public class XsgcMessageRemindRespDTO implements Serializable {

    /**
     * id
     */
    @Excel(name = "id", width = 25)
    private String id;

    /**
     * 工程id
     */
    @Excel(name = "工程id", width = 25)
    private String projectId;

    /**
     * 消息类型，1-预约安装提醒，2-预约回收提醒，3-现场勘察提醒，4-上门安装提醒，5-上门回收提醒，6-已接入监管的提醒，7-结束监管的提醒，8-违规告警的提醒
     */
    @Excel(name = "消息类型，1-预约安装提醒，2-预约回收提醒，3-现场勘察提醒，4-上门安装提醒，5-上门回收提醒，6-已接入监管的提醒，7-结束监管的提醒，8-违规告警的提醒", width = 25)
    private Integer type;

    /**
     * 关联id，1~7是关联monitor_flow的id, 8是关联aiot_event的id
     */
    @Excel(name = "关联id，1~7是关联monitor_flow的id, 8是关联aiot_event的id", width = 25)
    private String relateId;

    /**
     * 是否已读,0-未读，1-已读
     */
    @Excel(name = "是否已读,0-未读，1-已读", width = 25)
    private Integer status;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容", width = 25)
    private String content;

    /**
     * 备注信息，json字符串格式
     */
    @Excel(name = "备注信息，json字符串格式", width = 25)
    private String memo;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间", width = 25)
    private java.util.Date createdAt;

    /**
     * 更新时间
     */
    @Excel(name = "更新时间", width = 25)
    private java.util.Date updatedAt;

    /**
     * 用户id
     */
    @Excel(name = "用户id", width = 25)
    private String userId;

}
