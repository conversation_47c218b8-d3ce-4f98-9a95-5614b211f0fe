package cn.shencom.model.dto.resp;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 统计数据响应DTO
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class StatisticsRespDTO implements Serializable {

    @Getter
    @Setter
    class StatisticsRespDTO1 {
        /** 施工单位数量 */
        private Long contractingUnitCount;
        /** 工程总数 */
        private Long totalProjectCount;
        /** 施工中工程数量 */
        private Long ongoingProjectCount;
        /** 工程总金额（万元） */
        private BigDecimal totalAmount;
    }

    /**
     * 施工单位数量
     */
    private Long contractingUnitCount;

    /**
     * 工程总数
     */
    private Long totalProjectCount;

    /**
     * 施工中工程数量
     */
    private Long ongoingProjectCount;

    /**
     * 工程总金额（万元）
     */
    private BigDecimal totalAmount;
}
