package cn.shencom.model.dto.query;

import cn.shencom.scloud.common.jpa.util.query.ScBaseBean;
import lombok.*;

import java.io.Serializable;

/**
 * 摄像头表DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.EventCameraPointDevice
 * @since 2025-05-16
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
public class EventCameraPointDeviceQueryDTO extends ScBaseBean implements Serializable {

    /**
     * 真实状态
     */
    private Integer realStatus;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否健康 0-否 1-是
     */
    private Integer isHealthy;

    /**
     * 设备型号
     */
    private String modelNo;
}
