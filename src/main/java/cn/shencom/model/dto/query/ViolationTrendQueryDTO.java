package cn.shencom.model.dto.query;

import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 违规告警趋势统计查询DTO
 *
 * <AUTHOR>
 * @since 2025-07-21
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class ViolationTrendQueryDTO implements Serializable {

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;
}
