package cn.shencom.model.dto.create;

import lombok.*;

import java.io.Serializable;

/**
 * 小散工程-监管工单流程DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorFlow
 * @since 2025-07-05
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorFlowCreateDTO implements Serializable {

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;

    /**
     * 详情id ，关联
     */
    private String relateId;

    private String projectId;

    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束
     */
    private Integer flow;

    /**
     * 当前节点状态，0-未完成，1-已完成
     */
    private Integer state;

    /**
     * 当前节点结束时间
     */
    private java.util.Date finishTime;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;

}
