package cn.shencom.model.dto.update;

import lombok.*;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;

/**
 * 小散工程-监管工单流程DTO
 *
 * <AUTHOR>
 * @see cn.shencom.model.MonitorFlow
 * @since 2025-07-05
 */
@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class MonitorFlowUpdateDTO implements Serializable {

    /**
     * id
     */
    @NotBlank(message = "id不能为空")
    private String id;

    /**
     * 工单id，关联monitor_order
     */
    private String orderId;


    /**
     * 项目id
     */
    private String projectId;


    /**
     * 详情id ，关联
     */
    private String relateId;


    //流程节点不允许更改
    /**
     * 当前流程, 0-工程创建，1-安装预约，2-现场勘察，3-上门安装，4-接入监管， 5-施工完成，6-回收预约，7-上门回收，8-移除监管设备， 9-监管结束
     */
    private Integer flow;

    /**
     * 当前节点状态，0-未完成，1-已完成
     */
    private Integer state;

    /**
     * 当前节点结束时间
     */
    private java.util.Date finishTime;

    /**
     * 创建人
     */
    private String createdUser;

    /**
     * 修改人
     */
    private String updatedUser;


    //=============额外字段

    /**
     * 联系人电话
     */
    private String contactMobile;

    /**
     * 联系人姓名
     */
    private String contactName;



    // 预约安装需要的字段contactMobile，contactName，reservationTime
    /**
     * 预约时间
     */
    private java.util.Date reservationTime;




    //现场勘察需要的字段 memo，pic，inspectTime，installCnt

    /**
     * 勘察说明
     */
    private String memo;

    /**
     * 现场图片
     */
    private String pic;

    /**
     * 勘察时间
     */
    private java.util.Date inspectTime;

    /**
     * 评估需要安装的监控数
     */
    private Integer installCnt;


    //上门安装需要的字段  memo，pic，installTime，installCnt

    /**
     * 安装时间
     */
    private java.util.Date installTime;


    //预约回收需要的字段 contactMobile，contactMobile，reservationTime


    //上门回收的字段 recycleTime,recycleCnt,memo,pic

    /**
     * 回收时间
     */
    private java.util.Date recycleTime;

    /**
     * 回收摄像头数量
     */
    private Integer recycleCnt;


    //接入监管， 所需要的字段
    private List<String> cameraDeviceIds;

}
