package cn.shencom.utils;

import cn.shencom.enums.RegionLevelEnum;
import cn.shencom.model.EngineeringMembers;
import cn.shencom.model.EngineeringMembersProjectRelate;
import cn.shencom.model.FnRmsv3MembersTypeRelateBinding;
import cn.shencom.model.XsgcBusinessMembers;
import cn.shencom.model.dto.resp.FnRmsv3MembersTypeRelateRespDTO;
import cn.shencom.model.dto.resp.XsgcOrganizationRespDTO;
import cn.shencom.repos.EngineeringMembersRepository;
import cn.shencom.repos.XsgcBusinessMembersRepository;
import cn.shencom.scloud.common.base.exception.ScException;
import cn.shencom.scloud.common.base.util.BizAssert;
import cn.shencom.scloud.security.core.ScContext;
import cn.shencom.server.service.IFnRmsv3MembersTypeRelateService;
import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.nacos.common.utils.CollectionUtils;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户工具类
 * 提供用户权限校验、区域信息获取和项目查询条件构建等功能
 */
@Component
public class UserUtil {

    @Autowired
    private IFnRmsv3MembersTypeRelateService fnRmsv3MembersTypeRelateService;

    @Autowired
    private EngineeringMembersRepository engineeringMembersRepository;

    @Autowired
    private XsgcBusinessMembersRepository xsgcBusinessMembersRepository;

    /**
     * 区域信息内部类
     */
    @Getter
    @Setter
    public class UtilsRegion {
        /** 区级权限 */
        String regionPid;
        /** 街道权限 */
        String regionId;
        /** 社区权限 */
        String regionCid;
        /** 项目id */
        String projectId;
    }

    /**
     * 用户区域信息内部类
     * 用于存储用户关联的区域信息和权限级别
     */
    @Getter
    @Setter
    public class UserUtilsRegions {
        /** 区级权限 */
        Set<String> regionPidSet;
        /** 街道权限 */
        Set<String> regionIdSet;
        /** 社区权限 */
        Set<String> regionCidSet;
        /** 用户权限级别 */
        Integer level;
    }

    /**
     * 项目查询条件DTO内部类
     * 用于构建项目查询条件
     */
    @Getter
    @Setter
    public class UserUtilsProjectQueryDTO {
        /** 项目ID集合 */
        Set<String> projectIdSet;
        /** 区级权限 */
        Set<String> regionPidSet;
        /** 街道权限 */
        Set<String> regionIdSet;
        /** 社区权限 */
        Set<String> regionCidSet;
        /** 组织ID */
        String organizationId;
    }

    /**
     * 返回用户权限级别的区域id
     * 
     * 移除用户权限级别小于当前级别的区域权限
     * 
     * @param regions 用户区域权限信息对象
     * @return 过滤后的用户区域权限信息对象，如果用户没有权限则返回null
     */
    private Optional<UserUtilsRegions> filterLevelRegions(UserUtilsRegions regions) {

        UserUtilsRegions newRegions = new UserUtilsRegions();

        BeanUtils.copyProperties(regions, newRegions);

        Integer level = regions.getLevel();

        if (level == null) {
            return Optional.empty();
        }

        // 市级权限不进行过滤
        if (level == RegionLevelEnum.CITY.level()) {
            newRegions.setRegionPidSet(null);
            newRegions.setRegionIdSet(null);
            newRegions.setRegionCidSet(null);
            return Optional.of(newRegions);
        }

        if (level == RegionLevelEnum.STREET.level()) {
            // 街道权限需要 区级权限为空
            newRegions.setRegionPidSet(null);
        } else if (level == RegionLevelEnum.COMMUNITY.level()) {
            // 社区权限需要 区级, 街道级权限为空
            newRegions.setRegionPidSet(null);
            newRegions.setRegionIdSet(null);
        }

        return Optional.of(newRegions);
    }

    /**
     * 检查当前组织人员所属区域
     * 
     * @param userId
     * @param organizationId
     * @param project
     */
    public boolean checkRegion(String userId, String organizationId, UtilsRegion region) {
        // 获取用户所有的区域权限
        UserUtilsRegions userUtilsRegion = getUserRegionIds(userId, organizationId).orElse(null);

        if (userUtilsRegion == null) {
            throw new ScException("当前组织用户无所属区域！");
        }

        Integer level = userUtilsRegion.getLevel();

        // 市级权限不进行区域权限校验
        if (level == RegionLevelEnum.CITY.level()) {
            return true;
        }

        // 对比是否有权限
        String regionPid = region.getRegionPid();
        String regionId = region.getRegionId();
        String regionCid = region.getRegionCid();

        if (level == RegionLevelEnum.DISTRICT.level()) {
            return userUtilsRegion.getRegionPidSet().contains(regionPid);
        }

        if (level == RegionLevelEnum.STREET.level()) {
            return userUtilsRegion.getRegionIdSet().contains(regionId);
        }

        if (level == RegionLevelEnum.COMMUNITY.level()) {
            return userUtilsRegion.getRegionCidSet().contains(regionCid);
        }

        return false;
    }

    /**
     * 检查当前用户是否具有指定区域权限
     * 
     * @param region 区域信息
     * @return 如果用户具有指定区域权限返回true，否则返回false
     */
    public boolean checkRegion(UtilsRegion region) {
        String currentUser = ScContext.getCurrentUser().getId();

        String organizationId = XsgcContext.getOrganizationId();

        return checkRegion(currentUser, organizationId, region);
    }

    /**
     * 校验组织用户是否属于某个组织
     * 
     * @param userId         用户ID
     * @param organizationId 组织ID
     * @return 如果用户属于指定组织返回true，否则返回false
     * @throws ScException 当userId或organizationId为null时抛出异常
     */
    private boolean checkOrganization(String userId, String organizationId) {
        BizAssert.notNull(userId, "userId is null");
        BizAssert.notNull(organizationId, "organizationId is null");

        // 查找用户是否存在当前组织
        List<XsgcOrganizationRespDTO> organization = fnRmsv3MembersTypeRelateService
                .getMemberByOrganizationIdAndUserId(userId, organizationId);

        return organization.stream().anyMatch(o -> o.getId().equals(organizationId));
    }

    /**
     * 获取用户在指定组织下的区域权限信息
     * 
     * @param userId         用户ID
     * @param organizationId 组织ID
     * @return 用户区域权限信息对象，如果用户不属于该组织则返回null
     */
    private Optional<UserUtilsRegions> getUserRegionIds(String userId, String organizationId) {
        // 获取用户在指定组织下的区域权限信息
        FnRmsv3MembersTypeRelateRespDTO typeRelate = fnRmsv3MembersTypeRelateService
                .getMemberRelateByUserAndOrganization(userId, organizationId);

        if (typeRelate == null) {
            return Optional.empty();
        }

        Integer level = typeRelate.getLevel();

        List<FnRmsv3MembersTypeRelateBinding> regions = typeRelate.getRegionIds();

        if (regions.isEmpty() || level == null) {
            return Optional.empty();
        }

        UserUtilsRegions userRegions = new UserUtilsRegions();
        userRegions.setRegionPidSet(new HashSet<>());
        userRegions.setRegionIdSet(new HashSet<>());
        userRegions.setRegionCidSet(new HashSet<>());

        userRegions.setLevel(level);

        // 0 为无用权限,需要移除,不然查询会吧 0 当做条件
        regions = regions.stream().map(r -> {
            if (r.getRegionPid() != null && r.getRegionPid().equals("0")) {
                r.setRegionPid(null);
            }
            if (r.getRegionId() != null && r.getRegionId().equals("0")) {
                r.setRegionId(null);
            }
            if (r.getRegionCid() != null && r.getRegionCid().equals("0")) {
                r.setRegionCid(null);
            }
            return r;
        }).collect(Collectors.toList());

        regions.forEach(r -> {
            if (r.getRegionPid() != null) {
                userRegions.getRegionPidSet().add(r.getRegionPid());
            }
            if (r.getRegionId() != null) {
                userRegions.getRegionIdSet().add(r.getRegionId());
            }
            if (r.getRegionCid() != null) {
                userRegions.getRegionCidSet().add(r.getRegionCid());
            }
        });

        return Optional.of(userRegions);
    }

    /**
     * 获取用户角色权限的项目id集合
     */
    private Optional<Set<String>> getRolesProjectIds(String userId) {
        // 判断当前是否是业务人员，如果是则不受限制
        XsgcBusinessMembers xsgcBusinessMembers = xsgcBusinessMembersRepository
                .findFirstByUserIdAndStatus(userId, 1);

        // dto 什么查询条件都不存在, 就是所有的数据
        if (xsgcBusinessMembers != null) {
            return Optional.of(new HashSet<>());
        }

        // 查询当前用户是否是工程人员
        EngineeringMembers engineeringMembers = engineeringMembersRepository.findFirstByUserId(userId);

        if (engineeringMembers == null) {
            return Optional.empty();
        }

        // 如果是工程人员，查询这个工程人员关联的项目
        Set<String> projectIdSet = engineeringMembers.getMembersProjectRelateList()
                .stream().map(EngineeringMembersProjectRelate::getProjectId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(projectIdSet)) {
            return Optional.empty();
        }

        return Optional.of(projectIdSet);
    }

    /**
     * 获取组织区域
     */
    private Optional<UserUtilsRegions> getOrganizationRegions(String userId, String organizationId) {
        // 组织用户权限处理逻辑
        if (!checkOrganization(userId, organizationId)) {
            return Optional.empty();
        }

        UserUtilsRegions userUtilsRegions = getUserRegionIds(userId, organizationId).orElse(null);

        return Optional.of(userUtilsRegions);
    }

    /**
     * 过滤用户传过来的区域权限
     */
    private Optional<UserUtilsRegions> filterUserRegions(UserUtilsRegions userUtilsRegions, UtilsRegion region) {
        if (userUtilsRegions == null) {
            return Optional.empty();
        }

        UserUtilsRegions newUserRegions = new UserUtilsRegions();
        BeanUtils.copyProperties(userUtilsRegions, newUserRegions);

        Integer level = newUserRegions.getLevel();
        String regionPid = region.getRegionPid();
        String regionId = region.getRegionId();
        String regionCid = region.getRegionCid();

        if (StringUtils.isNotBlank(regionId)) {
            newUserRegions.setRegionIdSet(new HashSet<>(Arrays.asList(regionId)));
        }

        if (StringUtils.isNotBlank(regionCid)) {
            newUserRegions.setRegionCidSet(new HashSet<>(Arrays.asList(regionCid)));
        }

        if (StringUtils.isNotBlank(regionPid)) {
            newUserRegions.setRegionPidSet(new HashSet<>(Arrays.asList(regionPid)));
        }

        if (level == RegionLevelEnum.CITY.level()) {
            return Optional.of(newUserRegions);
        }

        Set<String> regionPidSet = userUtilsRegions.getRegionPidSet();
        Set<String> regionIdSet = userUtilsRegions.getRegionIdSet();
        Set<String> regionCidSet = userUtilsRegions.getRegionCidSet();

        if (regionPidSet != null && !regionPidSet.contains(regionPid)) {
            newUserRegions.setRegionPidSet(null);
        }

        if (regionIdSet != null && !regionIdSet.contains(regionId)) {
            newUserRegions.setRegionIdSet(null);
        }

        if (regionCidSet != null && !regionCidSet.contains(regionCid)) {
            newUserRegions.setRegionCidSet(null);
        }

        return Optional.of(newUserRegions);
    }

    /**
     * 根据当前用户和查询条件创建项目查询DTO
     * 
     * @param dto 散工项目查询DTO
     * @return 用户项目查询DTO，如果用户无权限则返回null
     * @throws ScException 当前用户不存在时抛出异常
     */
    public Optional<UserUtilsProjectQueryDTO> createUserProjectDto() {
        return createUserProjectDto(new UtilsRegion());
    }

    /**
     * 根据当前用户和查询条件创建项目查询DTO
     * 
     * @param dto 散工项目查询DTO
     * @return 用户项目查询DTO，如果用户无权限则返回null
     * @throws ScException 当前用户不存在时抛出异常
     */
    public Optional<UserUtilsProjectQueryDTO> createUserProjectDto(UtilsRegion region) {
        String userId = ScContext.getCurrentUser().getId();

        String organizationId = XsgcContext.getOrganizationId();
        return createUserProjectDto(userId, organizationId, region);
    }

    /**
     * 根据指定用户、组织和查询条件创建项目查询DTO
     * 
     * @param userId         用户ID
     * @param organizationId 组织ID
     * @param dto            散工项目查询DTO
     * @return 用户项目查询DTO，如果用户无权限则返回null
     */
    public Optional<UserUtilsProjectQueryDTO> createUserProjectDto(String userId, String organizationId,
            UtilsRegion dto) {

        String regionPid = dto.getRegionPid();
        String regionId = dto.getRegionId();
        String regionCid = dto.getRegionCid();

        UserUtilsProjectQueryDTO queryDTO = new UserUtilsProjectQueryDTO();

        if (StringUtils.isNotBlank(organizationId)) {
            UserUtilsRegions userUtilsRegions = getOrganizationRegions(userId, organizationId).orElse(null);

            if (StringUtils.isNotBlank(regionPid) || StringUtils.isNotBlank(regionId)
                    || StringUtils.isNotBlank(regionCid)) {
                userUtilsRegions = filterUserRegions(userUtilsRegions, dto).orElse(null);
            }

            userUtilsRegions = filterLevelRegions(userUtilsRegions).orElse(null);

            if (userUtilsRegions == null) {
                return Optional.empty();
            }

            if (userUtilsRegions.getRegionCidSet() != null) {
                queryDTO.setRegionCidSet(userUtilsRegions.getRegionCidSet());
            }
            if (userUtilsRegions.getRegionIdSet() != null) {
                queryDTO.setRegionIdSet(userUtilsRegions.getRegionIdSet());
            }
            if (userUtilsRegions.getRegionPidSet() != null) {
                queryDTO.setRegionPidSet(userUtilsRegions.getRegionPidSet());
            }

            queryDTO.setOrganizationId(organizationId);

        } else {
            Set<String> projectIdSet = getRolesProjectIds(userId).orElse(null);

            if (projectIdSet == null) {
                return Optional.empty();
            }

            // 如果项目id集合为空，没有权限限制
            if (projectIdSet.size() == 0) {
                return Optional.of(queryDTO);
            }

            if (StringUtils.isNotBlank(dto.getProjectId())) {
                // 没有项目权限则返回空
                if (!projectIdSet.contains(dto.getProjectId())) {
                    return Optional.empty();
                } else {
                    queryDTO.setProjectIdSet(new HashSet<>(Arrays.asList(dto.getProjectId())));
                }
            } else {
                queryDTO.setProjectIdSet(projectIdSet);
            }

            // 设置区域查询条件，优先使用最小级别的区域
            if (StringUtils.isNotBlank(regionCid)) {
                queryDTO.setRegionCidSet(new HashSet<>(Arrays.asList(regionCid)));
            } else if (StringUtils.isNotBlank(regionId)) {
                queryDTO.setRegionIdSet(new HashSet<>(Arrays.asList(regionId)));
            } else if (StringUtils.isNotBlank(regionPid)) {
                queryDTO.setRegionPidSet(new HashSet<>(Arrays.asList(regionPid)));
            }
        }

        return Optional.of(queryDTO);
    }
}
