package cn.shencom.server.service.impl;

import cn.shencom.model.dto.resp.StatisticsRespDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import javax.persistence.EntityManager;
import javax.persistence.Query;
import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 统计报表服务测试类
 *
 * <AUTHOR>
 * @since 2025-07-20
 */
public class ReportServiceImplTest {

    @Mock
    private EntityManager entityManager;

    @Mock
    private Query query;

    @InjectMocks
    private ReportServiceImpl reportService;

    @BeforeEach
    public void setUp() {
        // 初始化Mockito
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testGetStatistics() {

        // 模拟数据库查询结果
        when(entityManager.createNativeQuery(anyString())).thenReturn(query);
        when(query.getSingleResult())
                .thenReturn(25L)  // 施工单位数量
                .thenReturn(150L) // 工程总数
                .thenReturn(45L)  // 施工中工程数量
                .thenReturn(new BigDecimal("12505000")); // 工程总金额（元）

        // 执行统计查询
        StatisticsRespDTO statistics = reportService.getStatistics();

        // 验证返回结果不为空
        assertNotNull(statistics);
        assertNotNull(statistics.getContractingUnitCount());
        assertNotNull(statistics.getTotalProjectCount());
        assertNotNull(statistics.getOngoingProjectCount());
        assertNotNull(statistics.getTotalAmount());

        // 验证具体数值
        assertEquals(25L, statistics.getContractingUnitCount());
        assertEquals(150L, statistics.getTotalProjectCount());
        assertEquals(45L, statistics.getOngoingProjectCount());
        assertEquals(new BigDecimal("1250.50"), statistics.getTotalAmount());

        // 验证数据合理性
        assertTrue(statistics.getContractingUnitCount() >= 0);
        assertTrue(statistics.getTotalProjectCount() >= 0);
        assertTrue(statistics.getOngoingProjectCount() >= 0);
        assertTrue(statistics.getTotalAmount().compareTo(BigDecimal.ZERO) >= 0);

        // 验证施工中工程数量不应超过工程总数
        assertTrue(statistics.getOngoingProjectCount() <= statistics.getTotalProjectCount());

        // 打印统计结果
        System.out.println("统计结果:");
        System.out.println("施工单位数量: " + statistics.getContractingUnitCount());
        System.out.println("工程总数: " + statistics.getTotalProjectCount());
        System.out.println("施工中工程数量: " + statistics.getOngoingProjectCount());
        System.out.println("工程总金额: " + statistics.getTotalAmount() + "万元");
    }
}
