
# 设置 scid
@scid = sc047f71a5d4ef1134

# 设置 organizationId
@organizationId = 1711957904168636416

# 设置 Authorization
@Authorization = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbmlkIjoiOGU2YTk0NzBlOGIxNGFhYjliOGU1ZjMzOWMwODRkOWQiLCJ1c2VyX25hbWUiOiIxNzEzNzU5NzI3Mzg1OTg1MDI0IiwicGlkIjoiIiwiaXNCaW5kV3giOmZhbHNlLCJ0eXBlIjoxLCJhdXRob3JpdGllcyI6WyJzeXM6dXNlcjp1cGRhdGUiLCJzaG93Onp6Z2xkYXRhIiwic3lzOnVzZXI6aW5kZXgiLCJzeXM6dXNlcjpjcmVhdGUiXSwiY2xpZW50X2lkIjoic2Nsb3VkIiwicmVhbG5hbWUiOiLlrosqKiIsInVpZCI6IjE3MTM3NTk3MjgwMTkzMjQ5MjgiLCJ1c2VyQXV0aFR5cGUiOiJzeXMiLCJwaG9uZSI6IjE4NioqKioxMjg4Iiwic2NvcGUiOlsiYWxsIl0sImlkIjoiMTcxMzc1OTcyODAxOTMyNDkyOCIsImV4cCI6MTc1MzExNzI5MywianRpIjoiMTNjYWYyMjQtZWRjYS00MjMxLTgxOTQtYzdiMTJmZTM3M2MzIiwic2NpZCI6InNjMDQ3ZjcxYTVkNGVmMTEzNCIsImpvYk51bWJlciI6IlNDVDE3NTI5MTQ4NjQ3OTA1NTMiLCJ1c2VybmFtZSI6IjE3MTM3NTk3MjczODU5ODUwMjQifQ.LzBkD7wwGler10dzQHCWoYRFTGV92_K4KTzb0_g3Q2I

# 获取事件列表
POST /aiot/event/index HTTP/1.1
Host: 127.0.0.1:12245
Content-Type: application/json
Connection: keep-alive
Content-Length: 16
scid: {{scid}}
organizationId: {{organizationId}}
Authorization: bearer {{Authorization}}

{
    "size":1
}

# 获取统计数据
POST /report/project/statistics HTTP/1.1
Host: 127.0.0.1:12245
Content-Type: application/json
Connection: keep-alive
Content-Length: 16
scid: {{scid}}
organizationId: {{organizationId}}
Authorization: bearer {{Authorization}}
